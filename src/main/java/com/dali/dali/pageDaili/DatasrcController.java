package com.dali.dali.pageDaili;

import com.dali.dali.until.HttpsPostRequestExample;
import com.dali.dali.until.SymmetricEncryptionExample;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/***
 * 获取引用片段，查看文件的来源
 */
@Controller
public class DatasrcController {

    @RequestMapping("/message")
    public String message(HttpServletRequest request, ModelMap model) throws Exception {

        String number = request.getParameter("number");

        if(number == null || "".equals(number) || "0".equals(number)){
            return "index";
        }

        List<Map> reMapList = new ArrayList<>();

        String url = "http://10.11.2.160:3006/api/core/dataset/data/detail?id=";

        String contentvs = request.getParameter("contentvs");
        //苏二川__012652__2025-06-12
        String str = SymmetricEncryptionExample.decryptFromBase32(contentvs);
        String[] split = str.split("__");
        String username = split[0];
        String jobnumber = split[1];

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String newTime = dateFormat.format(new Date());


        for (int i = 0; i < Integer.valueOf(number); i++) {
            String id = request.getParameter("id"+(i+1));
            Map mapT = HttpsPostRequestExample.sendHutool2(url+id);

            //用于水印测试
            //1、获取文件集合id
            String collectionId = (String) mapT.get("collectionId");
            //2、根据集合id获得文件url地址
            String previewUrl = HttpsPostRequestExample.getFileUrlByCollectionId(collectionId, username, jobnumber, newTime);
            //进行加密校验
            previewUrl +=  "&contentb="+SymmetricEncryptionExample.encryptToBase32(username+"-"+jobnumber+"_"+newTime);

            mapT.put("fileurl",previewUrl);
            reMapList.add(mapT);
        }


        //显示水印
        // 创建水印配置对象
        // 创建水印配置对象
        Map<String, Object> watermarkConfig = new HashMap<>();
        watermarkConfig.put("width", 320);                // 单个水印的宽度（像素）
        watermarkConfig.put("height", 320);               // 单个水印的高度（像素）
        watermarkConfig.put("opacity", 0.2);             // 水印透明度（0.0-1.0）
        watermarkConfig.put("rotate", -45);               // 水印旋转角度（度）
        watermarkConfig.put("fontSize", 20);              // 水印文字大小（像素）
        watermarkConfig.put("color", "black");              // 水印文字颜色（CSS颜色值）
        watermarkConfig.put("fontWeight", "normal");        // 水印文字粗细（normal/bold）
        watermarkConfig.put("gapX", 20);                  // 水印水平间距（像素）
        watermarkConfig.put("gapY", 20);                  // 水印垂直间距（像素）
        watermarkConfig.put("text", username+"-"+jobnumber+"_"+newTime); // 水印显示的文本内容
        watermarkConfig.put("randomOffset", 30);          // 随机偏移量（像素）
        watermarkConfig.put("useGrid", false);            // 是否使用网格系统（false表示使用绝对定位）

        // 添加到模型
        model.addAttribute("watermarkConfig", watermarkConfig);

        model.addAttribute("list", reMapList);
        return "message";
    }

    @CrossOrigin
    @RequestMapping("/getFileUrlByCollectionId")
    @ResponseBody
    public String getFileUrlByCollectionId(HttpServletRequest request) throws Exception {
        String collectionId = request.getParameter("collectionId");
        //校验不通过、直接返回官网
        if (collectionId == null || "".equals(collectionId)){
            return "https://www.bodor.cn";
        }

        //获得用户名、工号和时间
        String username = request.getParameter("username");
        String jobnumber = request.getParameter("jobnumber");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String newTime = dateFormat.format(new Date());

        //1、根据集合id获得文件url地址
        String previewUrl = HttpsPostRequestExample.getFileUrlByCollectionId(collectionId, username, jobnumber, newTime);
        //2、进行加密校验，不是每个url都能直接预览，需要加密
        previewUrl +=  "&contentb="+SymmetricEncryptionExample.encryptToBase32(username+"-"+jobnumber+"_"+newTime);
        return previewUrl;
    }

}
