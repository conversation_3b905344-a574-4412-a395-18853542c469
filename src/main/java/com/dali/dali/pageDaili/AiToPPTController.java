package com.dali.dali.pageDaili;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.xslf.usermodel.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.awt.*;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/***
 * 用ai转ppt的后端，该部分调用fastgpt接口，
 * 1、根据资料、进行生成大纲
 * 2、根据资料、进行内容填充
 */
@RestController
public class AiToPPTController {


    @PostMapping("/PPTMessage2")
    public String qxDeal2(@RequestBody String map) throws IOException {

        JSONArray data = JSONObject.parseObject(map).getJSONArray("data");


        String sourceFilePath = "/mnt/data/pptgen/标题2.pptx";

        String transition = "/mnt/data/pptgen/引导页.pptx";
        String content2 = "/mnt/data/pptgen/内容2.pptx";
        String content3 = "/mnt/data/pptgen/内容3.pptx";
        String content4 = "/mnt/data/pptgen/内容4.pptx";
        String content1 = "/mnt/data/pptgen/内容1.pptx";

        FileInputStream inputStream = new FileInputStream(sourceFilePath);
        XMLSlideShow ppt = new XMLSlideShow(inputStream);   //整个幻灯片

        FileInputStream inputStreamtransition = new FileInputStream(transition);
        XMLSlideShow transitionPpt = new XMLSlideShow(inputStreamtransition);  //两个模版1
        FileInputStream inputStreamcontent2 = new FileInputStream(content2);
        XMLSlideShow contentPpt2 = new XMLSlideShow(inputStreamcontent2);  //两个模版2
        FileInputStream inputStreamcontent3 = new FileInputStream(content3);
        XMLSlideShow contentPpt3 = new XMLSlideShow(inputStreamcontent3);  //两个模版3
        FileInputStream inputStreamcontent4 = new FileInputStream(content4);
        XMLSlideShow contentPpt4 = new XMLSlideShow(inputStreamcontent4);  //两个模版4
        FileInputStream inputStreamcontent1 = new FileInputStream(content1);
        XMLSlideShow contentPpt1 = new XMLSlideShow(inputStreamcontent1);  //两个模版4

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyy.MM.dd");

        int yindaoye = 0;   //引导页的目录数

        for (int i = 0; i < data.size(); i++) {
            //取出每一页
            JSONObject page = (JSONObject) data.get(i);  //每一页的内容都是一个json对象

            String type = page.getString("type");  //判断是什么类型

            if ("cover".equals(type)){  //如果是封面

                JSONObject data1 = page.getJSONObject("data");  //取出首页中的内容

                XSLFSlide xslfShapes1 = ppt.getSlides().get(0);  //第一页，首页

                List<XSLFShape> shapes1 = xslfShapes1.getShapes();  //每一页中的图形

                shapes1.forEach(shape -> {
                    // 填充文本框数据
                    if (shape instanceof XSLFTextShape) {
                        XSLFTextShape textBox = (XSLFTextShape) shape;
                        if (textBox.getTextParagraphs() != null && textBox.getTextParagraphs().size() > 0 && textBox.getTextParagraphs().get(0).getTextRuns() != null && textBox.getTextParagraphs().get(0).getTextRuns().size() > 0) {
                            String text = textBox.getText();
                            if (text.equals("{模版标题}")) {
                                textBox.setText(data1.getString("title"));
//                                XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
//                                textRun.setFontColor(Color.BLACK);
//                                textRun.setFontSize(54.00);
//                                textRun.setBold(true);
                            }
                            else if (text.equals("{模版副标题}")) {
                                textBox.setText(data1.getString("text"));
//                                XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
//                                textRun.setFontColor(Color.RED);
//                                textRun.setFontSize(24.00);
//                                textRun.setBold(true);
                            }
                            else if (text.equals("{时间}")) {
                                textBox.setText(dateFormat.format(new Date()));
//                                XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
//                                textRun.setFontColor(Color.WHITE);
//                                textRun.setFontSize(10.00);
//                                textRun.setBold(true);
                            }
                        }
                    }

                });

            }
            else if ("contents".equals(type)) {  //如果是目录

                JSONArray data1 = page.getJSONObject("data").getJSONArray("items");  //取出首页中的内容

                XSLFSlide xslfShapes2 = ppt.getSlides().get(1);  //第二页，目录

                List<XSLFShape> shapes2 = xslfShapes2.getShapes();  //每一页中的图形

                shapes2.forEach(shape -> {
                    // 填充文本框数据
                    if (shape instanceof XSLFTextShape) {
                        XSLFTextShape textBox = (XSLFTextShape) shape;
                        if (textBox.getTextParagraphs() != null && textBox.getTextParagraphs().size() > 0 && textBox.getTextParagraphs().get(0).getTextRuns() != null && textBox.getTextParagraphs().get(0).getTextRuns().size() > 0) {
                            String text = textBox.getText();
                            if (text.equals("{目录1}")) {
                                if (data1.size() >= 1){
                                    textBox.setText(data1.getString(0));
                                }else {
                                    textBox.setText("");
                                }
                                XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                textRun.setFontColor(Color.BLACK);
                                textRun.setFontSize(18.00);
                                textRun.setBold(true);
                            }
                            else if (text.equals("{目录2}")) {
                                if (data1.size() >= 2){
                                    textBox.setText(data1.getString(1));
                                }else {
                                    textBox.setText("");
                                }
                                XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                textRun.setFontColor(Color.BLACK);
                                textRun.setFontSize(18.00);
                                textRun.setBold(true);
                            }
                            else if (text.equals("{目录3}")) {
                                if (data1.size() >= 3){
                                    textBox.setText(data1.getString(2));
                                }else {
                                    textBox.setText("");
                                }
                                XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                textRun.setFontColor(Color.BLACK);
                                textRun.setFontSize(18.00);
                                textRun.setBold(true);
                            }
                            else if (text.equals("{目录4}")) {
                                if (data1.size() >= 4){
                                    textBox.setText(data1.getString(3));
                                }else {
                                    textBox.setText("");
                                }
                                XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                textRun.setFontColor(Color.BLACK);
                                textRun.setFontSize(18.00);
                                textRun.setBold(true);
                            }
                            else if (text.equals("{目录5}")) {
                                if (data1.size() >= 5){
                                    textBox.setText(data1.getString(4));
                                }else {
                                    textBox.setText("");
                                }
                                XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                textRun.setFontColor(Color.BLACK);
                                textRun.setFontSize(18.00);
                                textRun.setBold(true);
                            }
                            else if (text.equals("{目录6}")) {
                                if (data1.size() >= 6){
                                    textBox.setText(data1.getString(5));
                                }else {
                                    textBox.setText(" ");
                                }
                                XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                textRun.setFontColor(Color.BLACK);
                                textRun.setFontSize(18.00);
                                textRun.setBold(true);
                            }
                        }
                    }

                });


            }
            else if ("transition".equals(type)) {  //如果是引导页

                JSONObject data1 = page.getJSONObject("data");  //取出首页中的内容

                //引导页添加===========
                XSLFSlide newSpotSlide1 = copySlide(transitionPpt.getSlides().get(0), ppt, i);
                List<XSLFShape> shapes1 = newSpotSlide1.getShapes();

                for (int j = 0; j < shapes1.size(); j++) {
                    XSLFShape shape = shapes1.get(j);
                    if (shape instanceof XSLFTextShape) {
                        XSLFTextShape textBox = (XSLFTextShape) shape;
                        if (textBox.getTextParagraphs() != null && textBox.getTextParagraphs().size() > 0 && textBox.getTextParagraphs().get(0).getTextRuns() != null && textBox.getTextParagraphs().get(0).getTextRuns().size() > 0) {
                            String text = textBox.getText();
                            if (text.equals("{模版引导页title}")) {
//                                ++yindaoye;   //用于下面子内容标记
                                textBox.setText((++yindaoye)+"、"+data1.getString("title"));
//                                textBox.setText(data1.getString("title"));
                                XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                textRun.setFontColor(Color.BLACK);
                                textRun.setFontSize(48.00);
                                textRun.setBold(true);
                            }
                            else if (text.equals("{模版引导页text}")) {
                                textBox.setText(data1.getString("text"));
                                XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                textRun.setFontColor(Color.RED);
                                textRun.setFontSize(24.00);
                                textRun.setBold(true);
                            }
                        }
                    }
                }

            }
            else if ("content".equals(type)) {  //如果是内容

                JSONArray data1 = page.getJSONObject("data").getJSONArray("items");  //取出首页中的内容

                if (data1.size() == 2){
                    //内容添加============
                    XSLFSlide newSpotSlide2 = copySlide(contentPpt2.getSlides().get(0), ppt, i);
                    List<XSLFShape> shapes2 = newSpotSlide2.getShapes();

                    for (int j = 0; j < shapes2.size(); j++) {
                        XSLFShape shape = shapes2.get(j);
                        // 填充文本框数据
                        if (shape instanceof XSLFTextShape) {
                            XSLFTextShape textBox = (XSLFTextShape) shape;
                            if (textBox.getTextParagraphs() != null && textBox.getTextParagraphs().size() > 0 && textBox.getTextParagraphs().get(0).getTextRuns() != null && textBox.getTextParagraphs().get(0).getTextRuns().size() > 0) {
                                String text = textBox.getText();
                                if (text.equals("{模版内容title}")) {
                                    textBox.setText((yindaoye)+". "+page.getJSONObject("data").getString("title"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.BLACK);
                                    textRun.setFontSize(36.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容1title}")) {
                                    textBox.setText((yindaoye)+".1 "+((JSONObject)data1.get(0)).getString("title"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.BLACK);
                                    textRun.setFontSize(32.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容1text}")) {
                                    textBox.setText(((JSONObject)data1.get(0)).getString("text"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.red);
                                    textRun.setFontSize(28.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容2title}")) {
                                    textBox.setText((yindaoye)+".2 "+((JSONObject)data1.get(1)).getString("title"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.BLACK);
                                    textRun.setFontSize(32.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容2text}")) {
                                    textBox.setText(((JSONObject)data1.get(1)).getString("text"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.red);
                                    textRun.setFontSize(28.00);
                                    textRun.setBold(true);
                                }
                            }
                        }
                    }
                }
                else if (data1.size() == 3){
                    //内容添加============
                    XSLFSlide newSpotSlide2 = copySlide(contentPpt3.getSlides().get(0), ppt, i);
                    List<XSLFShape> shapes2 = newSpotSlide2.getShapes();

                    for (int j = 0; j < shapes2.size(); j++) {
                        XSLFShape shape = shapes2.get(j);
                        // 填充文本框数据
                        if (shape instanceof XSLFTextShape) {
                            XSLFTextShape textBox = (XSLFTextShape) shape;
                            if (textBox.getTextParagraphs() != null && textBox.getTextParagraphs().size() > 0 && textBox.getTextParagraphs().get(0).getTextRuns() != null && textBox.getTextParagraphs().get(0).getTextRuns().size() > 0) {
                                String text = textBox.getText();
                                if (text.equals("{模版内容title}")) {
                                    textBox.setText((yindaoye)+". "+page.getJSONObject("data").getString("title"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.BLACK);
                                    textRun.setFontSize(28.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容1title}")) {
                                    textBox.setText((yindaoye)+".1 "+((JSONObject)data1.get(0)).getString("title"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.BLACK);
                                    textRun.setFontSize(24.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容1text}")) {
                                    textBox.setText(((JSONObject)data1.get(0)).getString("text"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.red);
                                    textRun.setFontSize(20.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容2title}")) {
                                    textBox.setText((yindaoye)+".2 "+((JSONObject)data1.get(1)).getString("title"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.BLACK);
                                    textRun.setFontSize(24.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容2text}")) {
                                    textBox.setText(((JSONObject)data1.get(1)).getString("text"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.red);
                                    textRun.setFontSize(20.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容3title}")) {
                                    textBox.setText((yindaoye)+".3 "+((JSONObject)data1.get(2)).getString("title"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.BLACK);
                                    textRun.setFontSize(24.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容3text}")) {
                                    textBox.setText(((JSONObject)data1.get(2)).getString("text"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.red);
                                    textRun.setFontSize(20.00);
                                    textRun.setBold(true);
                                }
                            }
                        }

                    }

                }
                else if (data1.size() >= 4){
                    //内容添加============
                    XSLFSlide newSpotSlide2 = copySlide(contentPpt4.getSlides().get(0), ppt, i);
                    List<XSLFShape> shapes2 = newSpotSlide2.getShapes();

                    for (int j = 0; j < shapes2.size(); j++) {
                        XSLFShape shape = shapes2.get(j);
                        // 填充文本框数据
                        if (shape instanceof XSLFTextShape) {
                            XSLFTextShape textBox = (XSLFTextShape) shape;
                            if (textBox.getTextParagraphs() != null && textBox.getTextParagraphs().size() > 0 && textBox.getTextParagraphs().get(0).getTextRuns() != null && textBox.getTextParagraphs().get(0).getTextRuns().size() > 0) {
                                String text = textBox.getText();
                                if (text.equals("{模版内容title}")) {
                                    textBox.setText((yindaoye)+". "+page.getJSONObject("data").getString("title"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.BLACK);
                                    textRun.setFontSize(28.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容1title}")) {
                                    textBox.setText((yindaoye)+".1 "+((JSONObject)data1.get(0)).getString("title"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.BLACK);
                                    textRun.setFontSize(24.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容1text}")) {
                                    textBox.setText(((JSONObject)data1.get(0)).getString("text"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.red);
                                    textRun.setFontSize(20.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容2title}")) {
                                    textBox.setText((yindaoye)+".2 "+((JSONObject)data1.get(1)).getString("title"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.BLACK);
                                    textRun.setFontSize(24.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容2text}")) {
                                    textBox.setText(((JSONObject)data1.get(1)).getString("text"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.red);
                                    textRun.setFontSize(20.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容3title}")) {
                                    textBox.setText((yindaoye)+".3 "+((JSONObject)data1.get(2)).getString("title"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.BLACK);
                                    textRun.setFontSize(24.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容3text}")) {
                                    textBox.setText(((JSONObject)data1.get(2)).getString("text"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.red);
                                    textRun.setFontSize(20.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容4title}")) {
                                    textBox.setText((yindaoye)+".4 "+((JSONObject)data1.get(3)).getString("title"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.BLACK);
                                    textRun.setFontSize(24.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容4text}")) {
                                    textBox.setText(((JSONObject)data1.get(3)).getString("text"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.red);
                                    textRun.setFontSize(20.00);
                                    textRun.setBold(true);
                                }
                            }
                        }
                    }
                }
                else if (data1.size() == 1){
                    XSLFSlide newSpotSlide2 = copySlide(contentPpt1.getSlides().get(0), ppt, i);
                    List<XSLFShape> shapes2 = newSpotSlide2.getShapes();

                    for (int j = 0; j < shapes2.size(); j++) {
                        XSLFShape shape = shapes2.get(j);
                        // 填充文本框数据
                        if (shape instanceof XSLFTextShape) {
                            XSLFTextShape textBox = (XSLFTextShape) shape;
                            if (textBox.getTextParagraphs() != null && textBox.getTextParagraphs().size() > 0 && textBox.getTextParagraphs().get(0).getTextRuns() != null && textBox.getTextParagraphs().get(0).getTextRuns().size() > 0) {
                                String text = textBox.getText();
                                if (text.equals("{模版内容title}")) {
                                    textBox.setText((yindaoye)+". "+page.getJSONObject("data").getString("title"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.BLACK);
                                    textRun.setFontSize(36.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容1title}")) {
                                    textBox.setText((yindaoye)+".1 "+((JSONObject)data1.get(0)).getString("title"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.BLACK);
                                    textRun.setFontSize(32.00);
                                    textRun.setBold(true);
                                }
                                else if (text.equals("{模版内容1text}")) {
                                    textBox.setText(((JSONObject)data1.get(0)).getString("text"));
                                    XSLFTextRun textRun = textBox.getTextParagraphs().get(0).getTextRuns().get(0);
                                    textRun.setFontColor(Color.red);
                                    textRun.setFontSize(28.00);
                                    textRun.setBold(true);
                                }
                            }
                        }
                    }
                }
            }
            else if ("end".equals(type)) {  //如果是结束
//                System.out.println(page.get("data"));
            }
        }

        String path = String.valueOf(Math.abs(UUID.randomUUID().getLeastSignificantBits()));
        String namefile = path+".pptx";

        FileOutputStream fos = new FileOutputStream("/mnt/data/pptgen/apache-tomcat-9/webapps/datafile/"+namefile); //新建文件
        ppt.write(fos);

        inputStreamtransition.close();
        inputStreamcontent2.close();
        inputStreamcontent3.close();
        inputStreamcontent4.close();
        inputStreamcontent1.close();
        fos.close();
        ppt.close();

//        return "### PPT文件已生成，下载地址："+"https://ai.bodor.com:3000/download/"+path;
        return "### PPT已生成 [点击下载](https://ai.bodor.com:3000/download/"+path+")";
    }


    /**
     * 复制ppt单页
     * @param template 模板页
     * @param ppt      ppt
     * @param newIndex 复制页放置位置
     * @return 复制页
     */
    private static XSLFSlide copySlide(XSLFSlide template, XMLSlideShow ppt, int newIndex) {
        // 创建新的一页PPT，按模板的布局
        XSLFSlide newSlide = ppt.createSlide().importContent(template);
        // 排序（在PPT中的第几页）
        ppt.setSlideOrder(newSlide, newIndex);
        return newSlide;
    }

}
