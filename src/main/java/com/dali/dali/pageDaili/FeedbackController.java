package com.dali.dali.pageDaili;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.dali.dali.until.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Controller
public class FeedbackController {

    @Autowired
    private RedisService redisService;

    private final String UPLOAD_DIR = "/mnt/data/pptgen/apache-tomcat-9/webapps/filefeedback/";  // 自定义存储路径


    @GetMapping("/feedback")
    public String feedbackForm(Model model, HttpServletRequest request) {
        //1、这里获得是谁提交的反馈信息，并把反馈信息带到model中进行隐藏
        //先进行单点登入验证
        String tokenId = request.getParameter("tokenId");

        if (tokenId == null || "".equals(tokenId)){
//            model.addAttribute("jobNumber", "012652");
            return "feedback2";
        }

        //1、拿着token去验证该用户是否纯在
        // 设置请求参数
        String url = "https://oa.bodor.com:8443/portal/r/jd?cmd=com.actionsoft.apps.addons.sso_validate";
        String access_key = "2e5295e9-f46d-45e0-8de0-8a1281a6da5c";

        // 通过表单提交参数
        HttpResponse responseht = HttpRequest.post(url)
                .form("tokenId", tokenId)
                .form("access_key", access_key)
                .execute();

        if (responseht.isOk()) {
            String jsonStr = responseht.body();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("单点登入反馈意见时间：" + dateFormat.format(new Date()) + "，用户：" + jsonStr);

            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            JSONObject dataObject = jsonObject.getJSONObject("data");

            String uid = dataObject.getString("uid");     //用户唯一标识、工号
            boolean validate = dataObject.getBooleanValue("validate");    //true代表该授权有效

            if (validate) {  //判断授权是否有效果
                // 初始化空表单对象
                model.addAttribute("jobNumber", uid);
                return "feedback";
            }
        }
        return "feedback2";
    }

    @PostMapping("/feedback")
    public String handleFeedback(
            @RequestParam String jobNumber,
            @RequestParam String content,
            @RequestParam(required = false) MultipartFile attachment,
            Model model) {

        try {
            // 1、处理反馈内容（保存到数据库等）
            if (attachment != null && !attachment.isEmpty()) {
                // 1.1 处理附件 验证文件大小
                if (attachment.getSize() > 10 * 1024 * 1024) {
                    model.addAttribute("error", "文件大小不能超过10MB");
                    return "feedback";
                }

                // 生成唯一文件名
                String filename = UUID.randomUUID() + "_" + attachment.getOriginalFilename();
                SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
                String filetime = formatter.format(new Date())+"/";  //根据时间进行划分

                // 保存文件到指定路径
                Path path = Paths.get(UPLOAD_DIR +filetime+ filename);
                Files.createDirectories(path.getParent());
                attachment.transferTo(path);
                String fileUrl = "http://10.11.2.160:8080/filefeedback/"+filetime + filename; // 相对路径

                // 1.2、保存反馈信息到数据库
                // 构建 data JSON 对象
                Map<String, String> dataMap = new HashMap<>();
                dataMap.put("jobNumber", jobNumber);
                dataMap.put("content", content);
                dataMap.put("fileUrl", fileUrl);

                //生成要存储的名字
                SimpleDateFormat formatter2 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
                String keytime = formatter2.format(new Date());  //根据时间进行划分
                int num = (int) (Math.random() * 900 + 100);
                redisService.createMap(keytime+num,dataMap);

            }
            else {
                //没有上传附件的
                // 构建 data JSON 对象
                Map<String, String> dataMap = new HashMap<>();
                dataMap.put("jobNumber", jobNumber);
                dataMap.put("content", content);
                dataMap.put("fileUrl", "");

                //生成要存储的名字
                SimpleDateFormat formatter2 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
                String keytime = formatter2.format(new Date());  //根据时间进行划分
                int num = (int) (Math.random() * 900 + 100);
                redisService.createMap(keytime+num,dataMap);

            }
            //正确存储
            model.addAttribute("success", "反馈提交成功！");

        } catch (Exception e) {
            model.addAttribute("error", "提交失败：" + e.getMessage());
        }

        //继续保存工号，用户循环记录
        model.addAttribute("jobNumber", jobNumber);
        return "feedback";
    }

}
