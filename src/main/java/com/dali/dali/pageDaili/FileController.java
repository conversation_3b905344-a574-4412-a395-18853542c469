package com.dali.dali.pageDaili;

import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

@RestController
public class FileController {

    /***
     * 下载获取PPT
     * @param path
     * @return
     * @throws IOException
     */
    @GetMapping("/download/{path}")
    public ResponseEntity<InputStreamResource> downloadFile(@PathVariable String path) throws IOException {
        if (path == null || "".equals(path)) return null;

        // 替换为你要下载文件的实际 URL
        String fileUrl = "http://10.11.2.160:8080/datafile/"+path+".pptx";
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            InputStream inputStream = connection.getInputStream();
            InputStreamResource resource = new InputStreamResource(inputStream);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="+path+".pptx");

            // 返回文件
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(connection.getContentLength())
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.presentationml.presentation"))
                    .body(resource);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    /***
     * 小邦出题、下载excel
     * @param path
     * @return
     * @throws IOException
     */
    @GetMapping("/downloadxml/{path}")
    public ResponseEntity<InputStreamResource> downloadFilexml(@PathVariable String path) throws IOException {
        if (path == null || "".equals(path)) return null;

        // 替换为你要下载文件的实际 URL
        String fileUrl = "http://10.11.2.160:8080/ctxml/"+path+".xlsx";
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            InputStream inputStream = connection.getInputStream();
            InputStreamResource resource = new InputStreamResource(inputStream);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="+path+".xlsx");

            // 返回文件
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(connection.getContentLength())
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.presentationml.presentation"))
                    .body(resource);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }


    /***
     * AI回答的内容、下载word
     * @param path
     * @return
     * @throws IOException
     */
    @GetMapping("/downloadword/{path}")
    public ResponseEntity<InputStreamResource> downloadFileword(@PathVariable String path) throws IOException {
        if (path == null || "".equals(path)) return null;

        // 替换为你要下载文件的实际 URL
        String fileUrl = "http://10.11.2.160:8080/markDocxFile/"+path+".docx";
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            InputStream inputStream = connection.getInputStream();
            InputStreamResource resource = new InputStreamResource(inputStream);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="+path+".docx");

            // 返回文件
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(connection.getContentLength())
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.presentationml.presentation"))
                    .body(resource);
        } else {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    /***
     * 图片代理
     * @return
     */
    @GetMapping("/picture/{urltype}")
    public ResponseEntity<byte[]> displayImage(@PathVariable String urltype) {
        // 替换为你要显示的 PNG 图片的实际 URL
        String imageUrl = "http://10.11.2.160:8080/picture/"+urltype+".png";
        try {
            URL url = new URL(imageUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 获取图片的输入流
                InputStream inputStream = connection.getInputStream();
                byte[] imageBytes = inputStream.readAllBytes();

                // 设置响应头
                HttpHeaders headers = new HttpHeaders();
                // 对于 PNG 图片，明确指定 Content-Type
                headers.setContentType(MediaType.IMAGE_PNG);

                return new ResponseEntity<>(imageBytes, headers, HttpStatus.OK);
            } else {
                return new ResponseEntity<>(HttpStatus.NOT_FOUND);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


}
