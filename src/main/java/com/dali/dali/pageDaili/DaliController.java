package com.dali.dali.pageDaili;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dali.dali.local.PermissionController;
import com.dali.dali.local.clickOnOff;
import com.dali.dali.until.bpmmd5;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.usermodel.Range;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.view.RedirectView;
import org.springframework.web.util.UriUtils;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 * 页面的代理，进来打开的页面
 */
@EnableScheduling
@RestController
public class DaliController {

    // 代称到真实地址的映射
    private static final Map<String, String> aliasMap = Map.of(
            "AItoPPT", "http://***********:3006/chat/share?shareId=mp8fsq6kvjyidsx79dssxp9x",

            "CodeHelp","http://***********:3006/chat/share?shareId=prr4aj5xpm76mjfpr6m1y3w7",

//            "AIQA", "http://*************:3000/chat/share?shareId=vdp85jv4ketykd9f8e7uolpi"
//            "AIQA", "http://************:3006/chat/share?shareId=jgk0fh0c32y032ebmsrvc8en",

            "htcheck", "http://***********:3006/chat/share?shareId=b351p0ww0b1gmm11jws5q4o7",

            "CPQA","http://***********:3006/chat/share?shareId=b3puimhbav7ixufd1bcilg31",

            "ITQA","http://***********:3006/chat/share?shareId=pie3gr2sd9c2ixweq2uatn1f"

    );



    // 访问 /a/{code} 时，服务器代理请求真实地址，返回内容给客户端
    @GetMapping("/AI/{code}")
    public void alias(@PathVariable String code, HttpServletResponse response, HttpServletRequest request) throws IOException {

        // 获取请求中的所有Cookie
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                // 查找名为 "exampleCookie" 的Cookie,并验证cookie值
                if ("exampleCookie".equals(cookie.getName()) && "exampleValue20250420".equals(cookie.getValue())) {
//                    System.out.println("读取到的Cookie值为: " + cookie.getValue());

                    //验证成功进行跳转=======================================
                    String realUrl = aliasMap.get(code);
                    if (realUrl != null) {
                        RestTemplate restTemplate = new RestTemplate();
                        // 从真实地址获取内容（这里假设返回的是字符串类型的HTML）
                        ResponseEntity<String> upstreamResponse = restTemplate.getForEntity(realUrl, String.class);

                        // 获取真实响应中的Content-Type并设置到返回的response中
                        HttpHeaders headers = upstreamResponse.getHeaders();
                        MediaType contentType = headers.getContentType();
                        if (contentType != null) {
                            response.setContentType(contentType.toString());
                        } else {
                            response.setContentType("text/html;charset=UTF-8");
                        }

                        // 设置状态码
                        response.setStatus(upstreamResponse.getStatusCodeValue());
                        // 返回真实内容
                        response.getWriter().write(upstreamResponse.getBody());
                    } else {
                        response.sendError(HttpStatus.NOT_FOUND.value(), "Alias not found");
                    }

                }
            }
        }

    }


    // 产品问答，服务器代理请求真实地址，返回内容给客户端
    @GetMapping("/CP/QA")
    public void CPQA(HttpServletResponse response, HttpServletRequest request) throws IOException {

        String realUrl = aliasMap.get("CPQA");

        RestTemplate restTemplate = new RestTemplate();
        // 从真实地址获取内容（这里假设返回的是字符串类型的HTML）
        ResponseEntity<String> upstreamResponse = restTemplate.getForEntity(realUrl, String.class);

        // 获取真实响应中的Content-Type并设置到返回的response中
        HttpHeaders headers = upstreamResponse.getHeaders();
        MediaType contentType = headers.getContentType();

        if (contentType != null) {
            response.setContentType(contentType.toString());
        } else {
            response.setContentType("text/html;charset=UTF-8");
        }

        // 设置状态码
        response.setStatus(upstreamResponse.getStatusCodeValue());
        // 返回真实内容
        response.getWriter().write(upstreamResponse.getBody());
    }

    @GetMapping("/CP/htcheck")
    public void CPhtcheck(HttpServletResponse response, HttpServletRequest request) throws IOException {

        String realUrl = aliasMap.get("htcheck");

        RestTemplate restTemplate = new RestTemplate();
        // 从真实地址获取内容（这里假设返回的是字符串类型的HTML）
        ResponseEntity<String> upstreamResponse = restTemplate.getForEntity(realUrl, String.class);

        // 获取真实响应中的Content-Type并设置到返回的response中
        HttpHeaders headers = upstreamResponse.getHeaders();
        MediaType contentType = headers.getContentType();

        if (contentType != null) {
            response.setContentType(contentType.toString());
        } else {
            response.setContentType("text/html;charset=UTF-8");
        }

        // 设置状态码
        response.setStatus(upstreamResponse.getStatusCodeValue());
        // 返回真实内容
        response.getWriter().write(upstreamResponse.getBody());
    }


    /***
     * 1、初步请求，拿着tokenid去判断是否合法，若是合法写标识到cookie中
     * 2、再从重定向到代理页面，在代理页面判断是否有标识cookie，若是有就进行通过，没有就返回
     * @param response
     * @return
     */
    @GetMapping("/chickTokenIdAndsetCookie")
    public RedirectView setCookie(HttpServletResponse response, HttpServletRequest request) {


         //先进行单点登入验证
        String tokenId = request.getParameter("tokenId");
        String type = request.getParameter("type");
        System.out.println("type："+type);
        if (tokenId == null || "".equals(tokenId)) return null;

         //1、拿着token去验证该用户是否纯在
         // 设置请求参数
         String url = "https://oa.bodor.com:8443/portal/r/jd?cmd=com.actionsoft.apps.addons.sso_validate";
         String access_key = "2e5295e9-f46d-45e0-8de0-8a1281a6da5c";

         // 通过表单提交参数
         HttpResponse responseht = HttpRequest.post(url)
         .form("tokenId", tokenId)
         .form("access_key", access_key)
         .execute();

         if (responseht.isOk()) {
             String jsonStr = responseht.body();
             SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
             System.out.println("单点登入时间："+ dateFormat.format(new Date())+"，用户：" +jsonStr);

             JSONObject jsonObject = JSONObject.parseObject(jsonStr);
             JSONObject dataObject = jsonObject.getJSONObject("data");

             String uid = dataObject.getString("uid");     //用户唯一标识、工号
             boolean validate = dataObject.getBooleanValue("validate");    //true代表该授权有效

             if (validate) {  //判断授权是否有效果
             //在这里面，进行跳转
             // 创建一个新的Cookie对象，指定名称和值
                 Cookie cookie = new Cookie("exampleCookie", "exampleValue20250420");
                 // 设置Cookie的路径，这里设置为根路径，表示整个应用都可以访问该Cookie
                 cookie.setPath("/");
                 // 设置Cookie的有效期，单位为秒，这里设置为1小时*24*7
                 cookie.setMaxAge(3600*24*7);
                 // 将Cookie添加到响应中
                 response.addCookie(cookie);
                 // 重定向到读取Cookie的方法
                 return new RedirectView("/AI/"+type);
             }
         }
         return null;  //验证不通过
    }

    // 避免登入页面对外暴漏，不进行代理,

    /***
     * 这里去掉/chat/share，是用于内部使用，
     * 内部使用端口也得改掉，3000到4000
     * htpps改成http
     * @return
     */
    @GetMapping({"/", "/login", "/chat/share"})
//    @GetMapping({"/", "/login"})
    public String loginSrc() {
        return "此页面不存在";
    }


    @GetMapping("/favicon.ico")
    public void favicon(HttpServletResponse response) throws IOException {
        Resource resource = new ClassPathResource("static/favicon.ico");
        response.setContentType("image/x-icon");
        StreamUtils.copy(resource.getInputStream(), response.getOutputStream());
    }


    /***
     * 重拍模型的代理
     * @param map
     * @return
     */
    @PostMapping("/v2/rerank")
    public Map rerank(@RequestBody Map map,HttpServletRequest request){

        //1、token的验证
        String authHeader = request.getHeader("Authorization");
        if (authHeader == null || authHeader.isEmpty()) {
            throw new IllegalArgumentException("Authorization header is required");
        }
        if (!"Bearer sk-ywookfzasoelawqmhurrrgcezbghoylklexuauwfqjvopqgz".equals(authHeader)) {
            throw new IllegalArgumentException("token is error");
        }

        //2、进行转发
        HttpResponse response = HttpRequest.post("http://***********:9997/v1/rerank")
                .body(JSONObject.toJSON(map).toString())
                .execute();

        //3、返回内容
        return JSONObject.parseObject(response.body());
    }


    //↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓合同审批、支持多文件格式↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓

    @PostMapping("/api/common/file/upload")
    public Map fileupload(
            @RequestParam("metadata") String metadata,
            @RequestParam("bucketName") String bucketName,
            @RequestParam("file") MultipartFile file,
            @RequestParam("data") String data
    ) throws IOException {
        String originalFilename = file.getOriginalFilename();

        // 尝试解码（适用于 RFC 5987 编码的文件名）
        if (originalFilename != null) {
            try {
                originalFilename = UriUtils.decode(originalFilename, StandardCharsets.UTF_8);
            } catch (IllegalArgumentException e) {
                // 如果解码失败，尝试其他方式
                originalFilename = new String(originalFilename.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
            }
        }

        System.out.println("metadata: " + metadata);
        System.out.println("bucketName: " + bucketName);
        System.out.println("fileName: " + originalFilename);
        System.out.println("data: " + data);

        File fileToUpload;
        String uploadFilename = originalFilename;

        // 检查文件是否为.doc类型
        if (originalFilename != null && originalFilename.toLowerCase().endsWith(".doc")) {
            // 转换DOC为DOCX
            fileToUpload = convertDocToDocx(file, originalFilename);
            uploadFilename = originalFilename.replaceAll("(?i)\\.doc$", ".docx");
            System.out.println("已将DOC文件转换为DOCX: " + uploadFilename);
        } else {
            // 非DOC文件保持原样
            fileToUpload = convertMultipartFileToFile(file, originalFilename);
        }

        try {
            HttpResponse response = HttpRequest.post("http://***********:3006/api/common/file/upload")
                    .form("metadata", metadata)
                    .form("bucketName", bucketName)
                    .form("file", fileToUpload, uploadFilename)
                    .form("data", data)
                    .execute();

            System.out.println(response.body());
            return JSONObject.parseObject(response.body());
        } finally {
            // 删除临时文件
//            if (fileToUpload.exists() && !fileToUpload.delete()) {
//                System.out.println("警告: 临时文件删除失败: " + fileToUpload.getAbsolutePath());
//            }
        }
    }

    /**
     * 将MultipartFile对象转换为File对象
     */
    public File convertMultipartFileToFile(MultipartFile multipartFile, String originalFilename) throws IOException {
//        File file = new File("F:/modelBack/" + originalFilename);
        File file = new File("/mnt/data/daoli_soft/updatefile/" + originalFilename);
        try (InputStream inputStream = multipartFile.getInputStream();
             OutputStream outputStream = new FileOutputStream(file)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
        return file;
    }

    /**
     * 将DOC文件转换为DOCX格式
     */
    private File convertDocToDocx(MultipartFile docFile, String originalFilename) throws IOException {
        // 创建临时DOC文件
        File tempDocFile = convertMultipartFileToFile(docFile, originalFilename);

        // 创建目标DOCX文件
        String docxFilename = originalFilename.replaceAll("(?i)\\.doc$", ".docx");
//        File tempDocxFile = new File("F:/modelBack/" + docxFilename);
        File tempDocxFile = new File("/mnt/data/daoli_soft/updatefile/" + docxFilename);

        try {
            // 使用Apache POI进行格式转换
            FileInputStream fis = new FileInputStream(tempDocFile);
            HWPFDocument doc = new HWPFDocument(fis);
            XWPFDocument docx = new XWPFDocument();

            // 简单转换：提取文本内容并创建新的DOCX
            Range range = doc.getRange();
            XWPFParagraph para = docx.createParagraph();
            XWPFRun run = para.createRun();
            run.setText(range.text());

            // 保存为DOCX文件
            FileOutputStream fos = new FileOutputStream(tempDocxFile);
            docx.write(fos);

            fos.close();
            docx.close();
            doc.close();
            fis.close();

            return tempDocxFile;
        } catch (Exception e) {
            System.err.println("DOC转DOCX失败: " + e.getMessage());
            e.printStackTrace();
            // 转换失败时返回原始文件
            return tempDocFile;
        }
    }

    //↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑合同审批、支持多附件↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑



    //↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓敏感词替换↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
    /***
     * 用于存储获得的敏感词
     */
    public static  Map<String,String> Tmap = new HashMap<>();

    /***
     * 用于更新敏感词
     * @return
     */

    @GetMapping("/v1/updateMGtoMap")
    public Map updateMGtoMap(){

        // 目标URL
        String url = "https://oa.bodor.com:8443/portal/r/jd?cmd=com.bodor.openapi.sync.getMgctLists";
        Map<String, Object> params = new HashMap<>();

        //1、data 参数
        Map<String, Object> dataMap = new HashMap<>();
        String dataJsonString = JSONUtil.toJsonStr(dataMap);

        //2、sign参数
        long currentTimeMillis = System.currentTimeMillis();
        String datastr = "data="+dataJsonString+"&eid=b1030515-be57-4627-ad06-25ea3f1a6afc&time="+currentTimeMillis+"d4c018a7-f899-4dd2-b458-9f1ec03a4c9d";
        String sign = bpmmd5.encrypt(datastr);

        params.put("data", dataJsonString);
        params.put("eid", "b1030515-be57-4627-ad06-25ea3f1a6afc");
        params.put("time", currentTimeMillis+"");
        params.put("sign", sign);

        System.out.println(params);

        HttpResponse response = HttpRequest.post(url)
                .contentType("application/x-www-form-urlencoded")
                .form(params)
                .execute();

        String body = response.body();
        JSONArray mglist = JSON.parseArray(body);
        if (mglist.size() > 0){
            Tmap.clear();  //清除以前全部的 敏感对
            for (int i = 0; i < mglist.size(); i++) {
                JSONObject mg = mglist.getJSONObject(i);
                String MGCT = mg.getString("MGCT");
                String THW = mg.getString("THW");
//            System.out.println("敏感词："+MGCT+" ,需要替换："+THW);
                Tmap.put(MGCT,THW);  //上午、上班开始时间
            }
        }

        return Tmap;
    }

    // 每天中午12:31执行
    @Scheduled(cron = "0 31 12 * * ?")
    public void executeAtNoon() {
        if (clickOnOff.isOffClick) return;  //设置定时器是否关闭、本地运行时关闭
        System.out.println("中午12:31，进行敏感词的更新");
        //获得全部人员工号和名称，用于独立界面
        new PermissionController().updaAlljobnumber();

        //获得全部人员  工号和部门全路径id，用于独立界面
        new PermissionController().updaAlljobToid();

        //获得全部人员工号和名称，用于独立界面
        updateMGtoMap();
    }

    // 每天凌晨00:30执行
    @Scheduled(cron = "0 31 0 * * ?")
    public void executeAtMidnight() {
        if (clickOnOff.isOffClick) return;  //设置定时器是否关闭、本地运行时关闭
        System.out.println("凌晨00:30，进行敏感词的更新");
        //获得全部人员工号和名称，用于独立界面
        new PermissionController().updaAlljobnumber();

        //获得全部人员  工号和部门全路径id，用于独立界面
        new PermissionController().updaAlljobToid();

        //获得全部人员工号和名称，用于独立界面
        updateMGtoMap();
    }

    /***
     * 2025-06-28
     * 过滤敏感词，大模型调用时使用
     * @param inlist
     * @return
     */
    @PostMapping("/v1/filterSensitiveWords")
    public List filterSensitiveWords(@RequestBody String inlist){
        //用的阿里巴巴fastjson进行解析
        JSONArray list = JSONObject.parseArray(inlist);

        //遍历替换
        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);

            //1、需要替换的内容
            String contentq = (String) jsonObject.get("q");
            String contenta = (String) jsonObject.get("a");

            //2、使用entrySet()遍历键值对，进行替换
            for (Map.Entry<String, String> entry : Tmap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                contentq = contentq.replace(key, value);
                contenta = contenta.replace(key, value);
            }
            //3、替换后写回
            jsonObject.put("q",contentq);
            jsonObject.put("a",contenta);
        }
        //返回替换后的内容
        return list;
    }

    //↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑敏感词替换↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑



}