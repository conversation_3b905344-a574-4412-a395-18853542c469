package com.dali.dali.pageDaili;

import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Controller;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;

/***
 * 剩余的东西全部进行转发
 */
@Controller
@Order(100) // 确保优先匹配其它更精确的映射
public class FallbackRedirectController {

    //正式系统
    private static final String TARGET_DOMAIN = "http://10.11.2.160:3006";
    //测试系统
//    private static final String TARGET_DOMAIN = "http://192.168.1.225:1991";
    private final RestTemplate restTemplate = new RestTemplate();

    @RequestMapping("/**")
    public void proxy(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String path = request.getRequestURI();

        String queryString = request.getQueryString();
        StringBuilder targetUrlBuilder = new StringBuilder(TARGET_DOMAIN);
        targetUrlBuilder.append(path);
        if (queryString != null && !queryString.isEmpty()) {
            targetUrlBuilder.append("?").append(queryString);
        }
        String targetUrl = targetUrlBuilder.toString();

        HttpMethod method = HttpMethod.resolve(request.getMethod());

        restTemplate.execute(targetUrl, method, new RequestCallback() {
            @Override
            public void doWithRequest(ClientHttpRequest clientRequest) throws IOException {
                // Copy the request headers
                for (String headerName : java.util.Collections.list(request.getHeaderNames())) {
                    if ("host".equalsIgnoreCase(headerName)) {
                        continue;
                    }
                    for (String headerValue : java.util.Collections.list(request.getHeaders(headerName))) {
                        clientRequest.getHeaders().add(headerName, headerValue);
                    }
                }
                // Copy the request body for non-GET methods
                if (!HttpMethod.GET.equals(method) && request.getContentLength() != 0) {
                    StreamUtils.copy(request.getInputStream(), clientRequest.getBody());
                }
            }
        }, new ResponseExtractor<Void>() {
            @Override
            public Void extractData(ClientHttpResponse clientResponse) throws IOException {
                response.setStatus(clientResponse.getRawStatusCode());

                // Copy response headers
                HttpHeaders upstreamHeaders = clientResponse.getHeaders();
                for (String headerName : upstreamHeaders.keySet()) {
                    for (String headerValue : upstreamHeaders.get(headerName)) {
                        response.addHeader(headerName, headerValue);
                    }
                }

                // Using a buffer to stream response chunk by chunk
                InputStream bodyStream = clientResponse.getBody();
                byte[] buffer = new byte[8192]; // adjust the buffer size as needed
                int bytesRead;
                while ((bytesRead = bodyStream.read(buffer)) != -1) {
                    response.getOutputStream().write(buffer, 0, bytesRead);
                    response.getOutputStream().flush();  // flush after writing each chunk
                }
                return null;
            }
        });
    }

}