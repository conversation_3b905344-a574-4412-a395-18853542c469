package com.dali.dali.local;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dali.dali.until.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/***
 * 该类用于小邦独立界面中权限控制，中转信息安全运营系统
 * 2025年8月20日13:51:30
 * 现在暂时不使用，现在改成在服务器上
 */
@RestController
public class PermissionController {

    @Autowired
    RedisService redisService;

    //All 获得全部人员工号
    final String urlbase2 = "http://20.1.1.2:9090/qx/qxBase/getMessageByall";


    //All 获得全部人员、部门id信息，用于登入验证
    //2025年9月17日13:51:53，添加用点踩获取，部门全路径
    final String urlbase3 = "http://20.1.1.2:9090/qx/deptlogin/getMessage";

    //用于登入验证,人员信息
    final String urlbase4 = "http://20.1.1.2:9090/qx/userlogin/getFUserQx";

    //用于登入验证,组织、部门信息
    final String urlbase5 = "http://20.1.1.2:9090/qx/deptlogin/getFDeptQx";


    //用于知识库验证,人员信息
    final String urlbase6 = "http://20.1.1.2:9090/qx/qxBaseUser/getKBUserQx";

    //用于知识库验证,组织、部门信息
    final String urlbase7 = "http://20.1.1.2:9090/qx/qxBaseDept/getKBDeptQx";

    //用于获取全部的版本升级说明
    final String urlbase8 = "http://20.1.1.2:9090/qx/visionMe/AiVisionme";

    //用于获取全部的通知信息
    final String urlbase9 = "http://20.1.1.2:9090/qx/message/AiMessage";


    //全部人员工号，用于加密验证，<jobnumber,username>
    public static Map<String,String> Alljobnumber = new HashMap<>();

    //全部人员对应部门全路径id <jobnumber,deptpathid>用于登入验证
    public static Map<String,String> AlljobToid = new HashMap<>();
    //全部人员对应部门全路径id <jobnumber,deptpathPathName>用于点、踩
    public static Map<String,String> AlljobToPathName = new HashMap<>();


    //全部可登入人员  对应工号 和 权限 <jobnumber,qx>用于登入验证
    public static Map<String,String> AlljobToQx = new HashMap<>();

    //全部可登入  对应部门id 和 权限 <deptid,qx>用于登入验证
    public static Map<String,String> AlldeptToQx = new HashMap<>();



    //全部知识库访问  对应工号 和 权限 <knowbase,<jobnumber,qx>>用于知识库验证
    public static Map<String,Map<String,String>> AllKbJobToQx = new HashMap<>();

    //全部知识库访问  对应部门id 和 权限 <knowbase,<deptid,qx>>用于知识库验证
    public static Map<String,Map<String,String>> AllKbDeptToQx = new HashMap<>();

    // 用于存储全部版本更新信息，手动同步过来  [<key,value>,<key,value>....]
    public static List<Map> AllVersionMessage = new ArrayList<Map>();

    // 用于存储全部通知信息，手动同步过来  [<key,value>,<key,value>....]
    public static List<Map> AiMessage = new ArrayList<Map>();

    /***
     * 预准备，更新四个缓存
     * 1、全部人员工号，用于加密验证，<jobnumber,username>             Alljobnumber
     * 2、全部人员对应部门全路径id <jobnumber,deptpathid>用于登入验证   AlljobToid
     *      全部人员对应部门全路径id <jobnumber,deptpathPathName>用于点、踩    AlljobToPathName
     *
     * 3、全部可登入人员  对应工号 和 权限 <jobnumber,qx>用于登入验证    AlljobToQx
     * 4、对应部门id 和 权限 <deptid,qx>用于登入验证                   AlldeptToQx
     * 5、对应工号 和 权限 <knowbase,<jobnumber,qx>>用于知识库验证    AllKbJobToQx
     * 6、对应部门id 和 权限 <knowbase,<deptid,qx>>用于知识库验证     AllKbDeptToQx
     *
     * 7、存储全部版本更新信息 [<key,value>,<key,value>....] 用于存储全部版本更新信息   AllVersionMessage
     * 8、存储全部通知信息 [<key,value>,<key,value>....] 用于存储全部版本更新信息   AiMessage
     */
    @GetMapping("/preMessage")
    public void preMessage(){
        //工号对应名称---全部
        updaAlljobnumber();
        //工号对应部门ID---全部
        //工号对应部门全路径名称---全部
        updaAlljobToid();

        //登入权限初始化
        updaAlljobToQx();
        updaAlldeptIdToQx();
        //知识库权限初始化
        updaAllKbJobToQx();
        updaAllKbDeptToQx();

        //更新升级信息
        updaAllVersionMessage();
        //更新通知信息
        updaAiMessage();
    }

    /***
     * 2025年9月17日13:58:34
     * 点踩根据工号获取部门全路径名称
     */
    @PostMapping ("/bpmuser/bpm/onejb")
    public Map GetjobToPathName(@RequestBody Map map){
        String jobnumber = (String) map.get("jobnumber");  //获得工号
        String check = (String) map.get("check");  //获得验证信息
        Map<String,String> remap = new HashMap<>();  //返回信息
        if ("Bodor@2025-07-01".equals(check)) {  //进行验证
            String s = AlljobToPathName.get(jobnumber);
            remap.put("deptpath",s);
            return remap;
        }
        return remap;
    }


    /***
     * ==========================获得全部人员工号和名称==============================
     * 1
     */
    @GetMapping("/updaAlljobnumber")
    public Map updaAlljobnumber(){

        HttpResponse response = HttpRequest.post(urlbase2)
                .contentType("application/json")
                .body("{\n" +
                        "    \"check\":\"zifErOcL2pn5t2cDGGnuLd1iVKC5TvdVDvnnCv16wLusOocffWYOkk\"\n" +
                        "}")
                .execute();

        if (response.isOk()) {
            Alljobnumber.clear();  //先清空、再赋值
            String jsonStr = response.body();
            JSONArray jsonArray = JSONObject.parseArray(jsonStr);
            for (Object jb: jsonArray) {
                String jobnumber = ((JSONObject) jb).getString("jobnumber");
                String username = ((JSONObject) jb).getString("username");
                Alljobnumber.put(jobnumber,username);
            }
        }
        //返回内容
        return Alljobnumber;
    }


    /***
     * 获得所有人员权限
     * 2
     */
    @GetMapping("/getAlljobnumber")
    public String getAlljobnumber(HttpServletRequest request){

        String jobnumber = request.getParameter("jobnumber");
        boolean contains = Alljobnumber.containsKey(jobnumber);
        if (contains){
            return "1";   //返回1，包含
        }
        return "0";   //返回0，不包含
    }



    /***
     * ==========================获得全部人员  工号和部门全路径id，用于存储备用==============================
     * 1
     */
    @GetMapping("/updaAlljobToid")
    public Map<String, String> updaAlljobToid(){

        HttpResponse response = HttpRequest.post(urlbase3)
                .contentType("application/json")
                .body("{\n" +
                        "    \"check\": \"Bodor@2025-08-02\"\n" +
                        "}")
                .execute();

        if (response.isOk()) {
            AlljobToid.clear();  //先清空、再赋值
            AlljobToPathName.clear();  //点采、先清空
            String jsonStr = response.body();
            JSONArray jsonArray = JSONObject.parseArray(jsonStr);
            for (Object jb: jsonArray) {
                String key = ((JSONObject) jb).getString("jobnumber");
                String value = ((JSONObject) jb).getString("deptpathid");
                //获得部门全路径名
                String value2 = ((JSONObject) jb).getString("deptpathname");
                AlljobToid.put(key,value);
                AlljobToPathName.put(key,value2);
            }
        }
        //返回内容
        return AlljobToid;
    }

    /***
     * ===========================获得全部   存储升级说明信息==============================
     * 1、用于更新缓存中的版本信息
     */
    @GetMapping("/updaAllVersionMessage")
    public List<Map> updaAllVersionMessage(){

        HttpResponse response = HttpRequest.post(urlbase8)
                .contentType("application/json")
                .body("{\n" +
                        "    \"check\":\"AiVisionme@20250827\",\n" +
                        "    \"status\":\"发布\"\n" +
                        "}")
                .execute();

        if (response.isOk()) {
            AllVersionMessage.clear();  //先清空、再赋值
            String jsonStr = response.body();
            JSONArray jsonArray = JSONObject.parseArray(jsonStr);
            AllVersionMessage = jsonArray.toJavaList(Map.class);
        }
        //返回内容
        return AllVersionMessage;
    }

    /***
     * 2、用于获得所有的版本信息
     */
    @PostMapping("/getAllVersionMessage")
    public List<Map> getAllVersionMessage(){
        return AllVersionMessage;
    }

    /***
     * 3、用于更新缓存中的版本信息状态
     */
    @PostMapping("/upVByJobnumber")
    public void upVByJobnumber(@RequestBody Map map){
        String jobnumber = (String) map.get("jobnumber");  //工号
        String versionid = (String) map.get("versionid");  //就是序号

        redisService.setToDb1(jobnumber,versionid);
    }

    /***
     * ===========================获得全部   通知信息==============================
     * 1、用于同步更新缓存中的通知信息
     */
    @GetMapping("/updaAiMessage")
    public List<Map> updaAiMessage(){

        HttpResponse response = HttpRequest.post(urlbase9)
                .contentType("application/json")
                .body("{\n" +
                        "    \"check\":\"AiMessage@20250908\",\n" +
                        "    \"status\":\"发布\"\n" +
                        "}")
                .execute();

        if (response.isOk()) {
            AiMessage.clear();  //先清空、再赋值
            String jsonStr = response.body();
            JSONArray jsonArray = JSONObject.parseArray(jsonStr);
            AiMessage = jsonArray.toJavaList(Map.class);
        }
        //返回内容
        return AiMessage;
    }

    /***
     * 2、用于获得所有的通知信息
     */
    @PostMapping("/getAiMessage")
    public List<Map> getAiMessage(){
        return AiMessage;
    }

    /***
     * 3、用于更新缓存中的通知信息状态
     */
    @PostMapping("/upNByJobnumber")
    public void upNByJobnumber(@RequestBody Map map){
        String jobnumber = (String) map.get("jobnumber");  //工号
        String versionid = (String) map.get("versionid");  //就是序号

        redisService.setToDb2(jobnumber,versionid);
    }

    /***
     * ==========================获得可以登入人员的  工号 权限==============================
     * 1
     */
    @GetMapping("/updaAlljobToQx")
    public Map<String, String> updaAlljobToQx(){

        HttpResponse response = HttpRequest.post(urlbase4)
                .contentType("application/json")
                .body("{\n" +
                        "    \"check\": \"getFUserQx@20250801\"\n" +
                        "}")
                .execute();

        if (response.isOk()) {
            AlljobToQx.clear();  //先清空、再赋值
            String jsonStr = response.body();
            JSONArray jsonArray = JSONObject.parseArray(jsonStr);
            for (Object jb: jsonArray) {
                String key = ((JSONObject) jb).getString("jobnumber");
                String value = ((JSONObject) jb).getString("status");
                AlljobToQx.put(key,value);
            }
        }
        //返回内容
        return AlljobToQx;
    }

    /***
     * ==========================获得可以登入人员的  部门全路径id  部门权限==============================
     * 1
     */
    @GetMapping("/updaAlldeptIdToQx")
    public Map<String, String> updaAlldeptIdToQx(){

        HttpResponse response = HttpRequest.post(urlbase5)
                .contentType("application/json")
                .body("{\n" +
                        "    \"check\": \"getFDeptQx@20250801\"\n" +
                        "}")
                .execute();

        if (response.isOk()) {
            AlldeptToQx.clear();  //先清空、再赋值
            String jsonStr = response.body();
            JSONArray jsonArray = JSONObject.parseArray(jsonStr);
            for (Object jb: jsonArray) {
                JSONArray jsonArray1 = ((JSONObject) jb).getJSONArray("assessScopeId");
                String value = ((JSONObject) jb).getString("status");
                //遍历进行赋值
                for (Object str:jsonArray1) {
                    AlldeptToQx.put((String) str,value);
                }
            }
        }
        //返回内容
        return AlldeptToQx;
    }


    /***
     * 进行登入验证的
     * 前端的js程序写的程序
     */
    @PostMapping("/jobByName")
    public Map jobByName(@RequestBody Map imap){
        String jobNumber = (String) imap.get("jobNumber");
        Map remap = new HashMap<>();  //返回的内容
        remap.put("username",Alljobnumber.get(jobNumber));
        remap.put("status","禁止");
        List<String> knowbasepre = new ArrayList<>();
        remap.put("knowbaseqx",knowbasepre);  //存放

        List<Map> version = new ArrayList<>();  //新版本信息
        remap.put("version",version);  //存放

        List<Map> notice = new ArrayList<>();  //通知、信息通知
        remap.put("notice",notice);  //存放

        //=================Redis=====================
        //1、判断redis中是否对应工号和版本
        if (redisService.hasKeyInDb1(jobNumber)){  //判断是否有该工号key、得到该版本的序号
            String levelv = redisService.getFromDb1(jobNumber);
            int levelvint = Integer.parseInt(levelv);

            for (Map<String,String> versionT: AllVersionMessage) {
                String cpmaprv = versionT.get("xvhao");
                //根据序号进行比较
                int cpmaprvint = Integer.parseInt(cpmaprv);
                if(cpmaprvint > levelvint){  //若是取的版本大于缓存版本，进行添加、弹窗
                    version.add(versionT);
                }else {
                    break;
                }
            }
        }else {    //2、若是redis中没有，就直接显示全部
            remap.put("version",AllVersionMessage);  //存拿来的全部
        }

        //2、判断redis中对应通知信息版本
        if (redisService.hasKeyInDb2(jobNumber)){  //判断是否有该工号key、得到该版本的序号
            String levelv = redisService.getFromDb2(jobNumber);
            int levelvint = Integer.parseInt(levelv);

            for (Map<String,String> versionT: AiMessage) {
                String cpmaprv = versionT.get("xvhao");
                //根据序号进行比较
                int cpmaprvint = Integer.parseInt(cpmaprv);
                if(cpmaprvint > levelvint){  //若是取的版本大于缓存版本，进行添加、用于提示
                    notice.add(versionT);
                }else {
                    break;
                }
            }
        }else {    //2、若是redis中没有，就直接显示全部
            remap.put("notice",AiMessage);  //存拿来的全部
        }


        //0、设置该工号都能访问哪些知识库
        //2025年8月13日16:13:17，这里加上登入时查询出该工号对应的知识库
        //难点、知识库名字不统一，这里做个映射、解耦，独立界面 和 信息安全运营系统 知识名称解耦
        //2025年9月17日13:42:19，value：中文名，信息安全运营中存储。key：英文名，独立界面中存储
        Map<String,String> mapT = new HashMap<>();
        mapT.put("marketing","营销客服库");
        mapT.put("tech","研发知识库");
        mapT.put("searchOnline","联网搜索");
        mapT.put("xbct","小邦出题");

        //现在 人力库和财经库都能查看
        knowbasepre.add("product");
        knowbasepre.add("finance");

        //工号  jobNumber
        for (Map.Entry<String, String> entry : mapT.entrySet()) {
            //判断是否有权限
            if ("1".equals(IsPassByJobAndKb(entry.getValue(),jobNumber))){
                knowbasepre.add(entry.getKey());
            }
        }

        //1、先判断该工号是否是否有直接授权
        if (AlljobToQx.containsKey(jobNumber)){  //判断是直接授权该工号
            String status = AlljobToQx.get(jobNumber);  //获得状态
            if("允许".equals(status)){
                remap.put("status","允许");
                return remap;
            }else {
                return remap;
            }
        }

        //2、根据部门id进行判断
        String s = AlljobToid.get(jobNumber);  //获得这个工号的部门id
        if (s != null){
            while(true){
                //是否包含
                boolean b = AlldeptToQx.containsKey(s);
                if (b){  //若果包含
                    String status = AlldeptToQx.get(s);
                    //判断状态名称
                    if("允许".equals(status)){
                        remap.put("status","允许");
                        return remap;
                    }else {
                        return remap;
                    }
                }
                //获取上一级的目录
                s = processPath(s);
                if ("结束".equals(s))
                    return remap;
            }
        }

        //返回内容
        return remap;
    }


    //登入验证辅助程序
    public static String processPath(String path) {
        if (path == null || path.trim().isEmpty()) {
            return "结束";
        }

        // 按斜杠分割并过滤空字符串
        String[] parts = path.split("/");
        List<String> validParts = new ArrayList<>();
        for (String part : parts) {
            if (!part.isEmpty()) {
                validParts.add(part);
            }
        }

        // 如果只有一个有效部分，返回"结束"
        if (validParts.size() <= 1) {
            return "结束";
        }

        // 取前 n-1 个有效部分，用斜杠拼接并添加末尾斜杠
        List<String> resultParts = validParts.subList(0, validParts.size() - 1);
        return String.join("/", resultParts) + "/";
    }


    /***
     * 用于登入直接获取该工号对应的全部知识库权限
     */
    public String IsPassByJobAndKb(String knowBase,String jobNumber){
        //1、先判断该知识库工号的权限，是否被赋予
        if (AllKbJobToQx.containsKey(knowBase)){    //判断是否有该工号直接授权知识库
            Map<String, String> map = AllKbJobToQx.get(knowBase);//获得状态

            if (map.containsKey(jobNumber)){  //判断是否有该工号的授权
                String status = map.get(jobNumber);  //获得该工号的状态
                if("允许".equals(status)){
                    return "1";
                }else {
                    return "0";
                }
            }
        }

        //2、根据部门id进行判断
        String s = AlljobToid.get(jobNumber);  //获得这个工号的部门id
        Map<String, String> kbMap = AllKbDeptToQx.get(knowBase);//获得状态

        if (s != null && kbMap != null){
            while(true){
                //是否包含
                boolean b = kbMap.containsKey(s);
                if (b){  //若果包含
                    String status = kbMap.get(s);
                    //判断状态名称
                    if("允许".equals(status)){
                        return "1";
                    }else {
                        return "0";
                    }
                }
                //获取上一级的目录
                s = processPath(s);
                if ("结束".equals(s))
                    return "0";
            }
        }

        //返回内容
        return "0";
    }

// ========================================进行权限，知识库的验证==========================================

    /***
     * ==========================获得可以登入人员的  工号 权限==============================
     * 1
     */
    @GetMapping("/updaAllKbJobToQx")
    public Map<String, Map<String, String>> updaAllKbJobToQx(){

        HttpResponse response = HttpRequest.post(urlbase6)
                .contentType("application/json")
                .body("{\n" +
                        "    \"check\": \"getKBUserQx@20250805\"\n" +
                        "}")
                .execute();

        if (response.isOk()) {
            AllKbJobToQx.clear();  //先清空、再赋值
            String jsonStr = response.body();
            JSONArray jsonArray = JSONObject.parseArray(jsonStr);
            for (Object jb: jsonArray) {
                String knowbase = ((JSONObject) jb).getString("knowbase");  //获取知识库
                String key = ((JSONObject) jb).getString("jobnumber");
                String value = ((JSONObject) jb).getString("status");

                if (AllKbJobToQx.containsKey(knowbase)){  //如果已经有该数据库
                    Map<String, String> map = AllKbJobToQx.get(knowbase);
                    map.put(key,value);
                }else {
                    Map<String, String> mapT = new HashMap<>();
                    mapT.put(key,value);
                    AllKbJobToQx.put(knowbase,mapT);
                }
            }
        }
        //返回内容
        return AllKbJobToQx;
    }







    /***
     * ==========================获得可以登入人员的  部门全路径id  部门权限==============================
     * 1
     */
    @GetMapping("/updaAllKbDeptToQx")
    public Map<String, Map<String, String>> updaAllKbDeptToQx(){

        HttpResponse response = HttpRequest.post(urlbase7)
                .contentType("application/json")
                .body("{\n" +
                        "    \"check\": \"getKBDeptQx@20250805\"\n" +
                        "}")
                .execute();

        if (response.isOk()) {
            AllKbDeptToQx.clear();  //先清空、再赋值
            String jsonStr = response.body();
            JSONArray jsonArray = JSONObject.parseArray(jsonStr);
            for (Object jb: jsonArray) {
                String knowbase = ((JSONObject) jb).getString("knowbase");  //获取知识库
                JSONArray jsonArray1 = ((JSONObject) jb).getJSONArray("assessScopeId");
                String value = ((JSONObject) jb).getString("status");

                if (AllKbDeptToQx.containsKey(knowbase)){  //如果已经有该数据库
                    Map<String, String> map = AllKbDeptToQx.get(knowbase);
                    //遍历进行赋值
                    for (Object str:jsonArray1) {
                        map.put((String) str,value);
                    }
                }else {
                    Map<String, String> mapT = new HashMap<>();
                    //遍历进行赋值
                    for (Object str:jsonArray1) {
                        mapT.put((String) str,value);
                    }
                    AllKbDeptToQx.put(knowbase,mapT);
                }
            }
        }
        //返回内容
        return AllKbDeptToQx;
    }

    /***
     * 知识库权限判断，根据工号和知识库名
     * fastgpt流程中调用
     */
    @PostMapping("/IsPassByJobAndKb")
    public String IsPassByJobAndKb(@RequestBody Map imap){
        String knowBase = (String) imap.get("knowBase");
        String jobNumber = (String) imap.get("jobNumber");
        String check = (String) imap.get("check");
        if (!"bodorIsPassByJobAndKb".equals(check)) return "0";

        //1、先判断该知识库工号的权限，是否被赋予
        if (AllKbJobToQx.containsKey(knowBase)){    //判断是否有该工号直接授权知识库
            Map<String, String> map = AllKbJobToQx.get(knowBase);//获得状态

            if (map.containsKey(jobNumber)){  //判断是否有该工号的授权
                String status = map.get(jobNumber);  //获得该工号的状态
                if("允许".equals(status)){
                    return "1";
                }else {
                    return "0";
                }
            }
        }

        //2、根据部门id进行判断
        String s = AlljobToid.get(jobNumber);  //获得这个工号的部门id
        Map<String, String> kbMap = AllKbDeptToQx.get(knowBase);//获得状态

        if (s != null){
            while(true){
                //是否包含
                boolean b = kbMap.containsKey(s);
                if (b){  //若果包含
                    String status = kbMap.get(s);
                    //判断状态名称
                    if("允许".equals(status)){
                        return "1";
                    }else {
                        return "0";
                    }
                }
                //获取上一级的目录
                s = processPath(s);
                if ("结束".equals(s))
                    return "0";
            }
        }

        //返回内容
        return "0";
    }

}
