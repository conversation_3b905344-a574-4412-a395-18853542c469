package com.dali.dali.local;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dali.dali.until.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/***
 * 产品RAG、表格转化
 */
@RestController
public class CanPinQAController {

    @Autowired
    RedisService redisService;

    /***
     * 查询多张表
     * @param str
     * @return
     */
    @PostMapping("/getCPMessage")
    public String getCPMessage(@RequestBody String str){
        //用于返回的容器
        List<Map<String,String>> reList = new ArrayList<>();
        //解析传进来的参数
        JSONArray jsonArray = JSONObject.parseArray(str);

        //判断是否为空，
        if (jsonArray.size() == 0) return "-1";

        int i = 1;  //用于区分表，更有条例，感觉更适合大模型理解
        for (Object jsonObject: jsonArray) {
            String key = (String) ((JSONObject) jsonObject).get("a");
            String res = redisService.get(key);
            //进行存储返回
            Map<String, String> mapT = new HashMap<>();
            mapT.put("table"+i,res);
            reList.add(mapT);
            i++;
        }

        //返回查询的内容
        return JSON.toJSONString(reList);
    }

    /***
     * 查询单张表
     * @param str
     * @return
     */
    @PostMapping("/getCPMessageOne")
    public String getCPMessageOne(@RequestBody String str){
        //用于返回的容器
        List<Map<String,String>> reList = new ArrayList<>();
        //解析传进来的参数
        JSONArray jsonArray = JSONObject.parseArray(str);

        //判断是否为空，
        if (jsonArray.size() == 0) return "-1";

        //获得一个
        JSONObject jsonObject = jsonArray.getJSONObject(0);
        String key = (String) jsonObject.get("a");
        String res = redisService.get(key);
        //进行存储返回
        Map<String, String> mapT = new HashMap<>();
        mapT.put("table",res);
        reList.add(mapT);

        //返回查询的内容
        return JSON.toJSONString(reList);
    }

    /***
     * 查询两张表
     * @param str
     * @return
     */
    @PostMapping("/getCPMessageTwo")
    public String getCPMessageTwo(@RequestBody String str){
        //用于返回的容器
        List<Map<String,String>> reList = new ArrayList<>();
        //解析传进来的参数
        JSONArray jsonArray = JSONObject.parseArray(str);

        //判断是否为空，
        if (jsonArray.size() == 0) return "-1";

        int i = 1;  //用于区分表，更有条例，感觉更适合大模型理解
        for (Object jsonObject: jsonArray) {
            String key = (String) ((JSONObject) jsonObject).get("a");
            String res = redisService.get(key);
            //进行存储返回
            Map<String, String> mapT = new HashMap<>();
            mapT.put("table"+i,res);
            reList.add(mapT);
            i++;
            if (i == 3) break;
        }

        //返回查询的内容
        return JSON.toJSONString(reList);
    }


    /***
     * 新的查询方式，
     * 相当于加了一个准确度过滤
     * 1、先准确度过滤
     * 2、向量相识度过滤
     * @param str
     * @return
     */
    @PostMapping("/getCPMessageT")
    public String getCPMessageT(@RequestBody String str){

        //用于返回的容器
        String rs1 = "-1";
        //解析传进来的参数
        JSONArray jsonArray = JSONObject.parseArray(str);
        //存放变量，对应 全部信息
        Map<Set<String>,String> databaseall = new HashMap<>();
        Set<String> databasetotal = new HashSet<>();  //全部的

        {
            Set<String> databaseT1 = new HashSet<>();  //
            databaseT1.addAll(Arrays.asList("Pro 1500","Air 600","Air 900","Air 1200","Aqu 1200","Aqu1500","Aqu 2000","Aqu 3000","自冷却手持激光焊","Self-cooling Handheld Laser Welding User Guide"));
            databasetotal.addAll(databaseT1);
            databaseall.put(databaseT1,"handle_images");

            Set<String> databaseT2 = new HashSet<>();  //
            databaseT2.addAll(Arrays.asList("Dream6","Dream4","Dream3","Dream0","Dream1"));
            databasetotal.addAll(databaseT2);
            databaseall.put(databaseT2,"Dream_images");

            Set<String> databaseT3 = new HashSet<>();  //
            databaseT3.addAll(Arrays.asList("A14","A13","A12","A8","A6L Plus","A6 Plus","A6M","A6","A4 Plus","A4","A3","A3 CE"));
            databasetotal.addAll(databaseT3);
            databaseall.put(databaseT3,"A_images");

            Set<String> databaseT4 = new HashSet<>();  //
            databaseT4.addAll(Arrays.asList("EA3","EA6 Plus"));
            databasetotal.addAll(databaseT4);
            databaseall.put(databaseT4,"EA_images");

            Set<String> databaseT5 = new HashSet<>();  //
            databaseT5.addAll(Arrays.asList("B6","B4","B3"));
            databasetotal.addAll(databaseT5);
            databaseall.put(databaseT5,"B_images");

            Set<String> databaseT6 = new HashSet<>();  //
            databaseT6.addAll(Arrays.asList("C12","C6","C4","C3"));
            databasetotal.addAll(databaseT6);
            databaseall.put(databaseT6,"C_images");

            Set<String> databaseT7 = new HashSet<>();  //
            databaseT7.addAll(Arrays.asList("EC3","EC6"));
            databasetotal.addAll(databaseT7);
            databaseall.put(databaseT7,"EC_images");

            Set<String> databaseT8 = new HashSet<>();  //
            databaseT8.addAll(Arrays.asList("P12","P8","P6","P4","P3"));
            databasetotal.addAll(databaseT8);
            databaseall.put(databaseT8,"P_images");

            Set<String> databaseT9 = new HashSet<>();  //
            databaseT9.addAll(Arrays.asList("G3000","G4000","G5000","G3000-28","G3000-26","G3000-24","G3000-22","G3000-20","G3000-18","G3000-16","G3000-14","G3000-12","G3000-6","G4000-28","G4000-24","	G4000-20","G4000-16","G4000-12","G5000-24","G5000-20","G5000-16","G5000-12"));
            databasetotal.addAll(databaseT9);
            databaseall.put(databaseT9,"G_images");

            Set<String> databaseT10 = new HashSet<>();  //
            databaseT10.addAll(Arrays.asList("H3000","H4000","H5000","H3000-24","H3000-20","H3000-16","H3000-12","H4000-24","H4000-20","H4000-16","H4000-12","H5000-32"));
            databasetotal.addAll(databaseT10);
            databaseall.put(databaseT10,"H_images");

            Set<String> databaseT11 = new HashSet<>();  //
            databaseT11.addAll(Arrays.asList("L2500","L3000","L3500","L4000","L2500-6","L2500-8","L3000-12","L3000-14","L3000-16","L3000-18","L3000-20","L3000-22","L3000-24","L3000-26","L3500-12","L3500-14","	L3500-16","L3500-20","L3500-24","L3500-26","L4000-12","L4000-14","L4000-16","L4000-20","L4000-24","L4000-26"));
            databasetotal.addAll(databaseT11);
            databaseall.put(databaseT11,"L_images");

            Set<String> databaseT12 = new HashSet<>();  //
            databaseT12.addAll(Arrays.asList("i7","i6","i5"));
            databasetotal.addAll(databaseT12);
            databaseall.put(databaseT12,"i_images");

            Set<String> databaseT13 = new HashSet<>();  //
            databaseT13.addAll(Arrays.asList("Rocut4","Rocut3"));
            databasetotal.addAll(databaseT13);
            databaseall.put(databaseT13,"Rocut_images");

            Set<String> databaseT14 = new HashSet<>();  //
            databaseT14.addAll(Arrays.asList("A6T Plus","A6T","A4T","A3T"));
            databasetotal.addAll(databaseT14);
            databaseall.put(databaseT14,"AT_images");

            Set<String> databaseT15 = new HashSet<>();  //  ----------------
            databaseT15.addAll(Arrays.asList("C3T"));
            databasetotal.addAll(databaseT15);
            databaseall.put(databaseT15,"CT_images");

            Set<String> databaseT17 = new HashSet<>();  //
            databaseT17.addAll(Arrays.asList("料库系统 iTrans Tower3","料库系统 iTower3","全自动上下料系统 iTrans3","全自动上下料系统 iTrans6（非标发布）","全自动上下料系统iTrans4","自动上料模块iLoader3","自动上料模块 iLoader4","自动上料模块 iLoader6","辅助上料模块 iLoader eco3","辅助上料模块 iLoader eco4 "));
            databasetotal.addAll(databaseT17);
            databaseall.put(databaseT17,"板机自动化装置_images");

            Set<String> databaseT18 = new HashSet<>();  //
            databaseT18.addAll(Arrays.asList("Dream系列","P系列","H系列","L系列","G系列","C系列","EC系列","B系列","A系列","EA系列","i系列","R系列","CT系列","AT系列"));
            databasetotal.addAll(databaseT18);
            databaseall.put(databaseT18,"板机选配项_images");

            Set<String> databaseT19 = new HashSet<>();  //
            databaseT19.addAll(Arrays.asList("空压机选配建议","设备功率段","是否扫描切","板材类型","喷嘴大小","切割板厚","推荐空压机"));
            databasetotal.addAll(databaseT19);
            databaseall.put(databaseT19,"空压机选型说明_images");

            Set<String> databaseT20 = new HashSet<>();  //
            databaseT20.addAll(Arrays.asList("去毛刺机","去毛刺机参数"));
            databasetotal.addAll(databaseT20);
            databaseall.put(databaseT20,"去毛刺机_images");

            Set<String> databaseT21 = new HashSet<>();  //
            databaseT21.addAll(Arrays.asList("Q0","Q0 Pro","Q0-65","Q0 Pro-65"));
            databasetotal.addAll(databaseT21);
            databaseall.put(databaseT21,"Q_images");

            Set<String> databaseT22 = new HashSet<>();  //
            databaseT22.addAll(Arrays.asList("Smart1","Smart1-65"));
            databasetotal.addAll(databaseT22);
            databaseall.put(databaseT22,"Smart_images");

            Set<String> databaseT23 = new HashSet<>();  //
            databaseT23.addAll(Arrays.asList("K0","K1","K1 Plus","K2","K3","K5","K0-65","K1-65","K1-65（非标）","K1 Plus-65","K2-65","K2-92","K2-65","K2-92","K3-65","K3-92","K3-120","K3-65","K3-92","K5-120"));
            databasetotal.addAll(databaseT23);
            databaseall.put(databaseT23,"K_images");

            Set<String> databaseT24 = new HashSet<>();  //
            databaseT24.addAll(Arrays.asList("K2 Pro","K3 Pro","K2 Pro-65","K2 Pro-92","K3 Pro-65","K3 Pro-92"));
            databasetotal.addAll(databaseT24);
            databaseall.put(databaseT24,"K Pro_images");

            Set<String> databaseT25 = new HashSet<>();  //
            databaseT25.addAll(Arrays.asList("S1","S2","S1-65","S2-65"));
            databasetotal.addAll(databaseT25);
            databaseall.put(databaseT25,"S_images");

            Set<String> databaseT26 = new HashSet<>();  //
            databaseT26.addAll(Arrays.asList("T2","T3","T2-65","T2-92","T3-65","T3-92"));
            databasetotal.addAll(databaseT26);
            databaseall.put(databaseT26,"T_images");

            Set<String> databaseT27 = new HashSet<>();  //
            databaseT27.addAll(Arrays.asList("N3","N3-65-65","N3-92-65","N3-92-92","N3-120-65","N3-120-92","N3-120-120"));
            databasetotal.addAll(databaseT27);
            databaseall.put(databaseT27,"N_images");

            Set<String> databaseT28 = new HashSet<>();  //
            databaseT28.addAll(Arrays.asList("M5","M3","M2","M5-120-120","M3-65-65","M3-92-65","M3-92-92","M3-120-65","M3-120-92","M3-120-120","M3-150-150","M2-65-35","M2-65-65"));
            databasetotal.addAll(databaseT28);
            databaseall.put(databaseT28,"M_images");

            Set<String> databaseT29 = new HashSet<>();  //
            databaseT29.addAll(Arrays.asList("U10","U10-125-125"));
            databasetotal.addAll(databaseT29);
            databaseall.put(databaseT29,"U_images");

            Set<String> databaseT30 = new HashSet<>();  //
            databaseT30.addAll(Arrays.asList("HCut","Hcut10-135-135"));
            databasetotal.addAll(databaseT30);
            databaseall.put(databaseT30,"HCut_images");

            Set<String> databaseT31 = new HashSet<>();  //
            databaseT31.addAll(Arrays.asList("M系列","N系列","T系列","Smart系列","K系列","K Pro系列","S系列","Q系列","U系列","Hcut系列"));
            databasetotal.addAll(databaseT31);
            databaseall.put(databaseT31,"管机通用选配项_images");

            Set<String> databaseT32 = new HashSet<>();  //
            databaseT32.addAll(Arrays.asList("自动上料装置","辅助下料装置","M-Trans2-65","T-Trans2-65","T-Trans2-92","S-Trans1-65","S-Trans2-65","K-Trans0-65","K-Trans0-65","K-Trans1-65","K-Trans1-65","K-Trans1-65","K-Trans1 Plus-65","K-Trans2-65","K-Trans2-65","M-Loader2-65","M-Loader3-65","M-Loader3-92","M-Loader3-120","M-Loader3-150","M-Loader5-120","N-Loader3-65","N-Loader3-92","N-Loader3-120","T-Loader2-65","T-Loader2-92","T-Loader3-65","T-Loader3-92	S-Loader1-65","S-Loader2-65","S-Loader2-92","K-Loader0-65","K-Loader0-65","K-Loader1-65	K-Loader1-65","K-Loader1-65","K-Loader1 Pro-65","K-Loader1 Plus-65","K-Loader1 Plus-65","K-Loader2-65","K-Loader2-65","K-Loader2-92","K-Loader2 Pro-65","K-Loader2 Pro-92","	K-Loader3-65","K-Loader3-92","K-Loader3 Pro-65","K-Loader3 Pro-92","K-Loader3 Pro-120","M-Unloader3-92"));
            databasetotal.addAll(databaseT32);
            databaseall.put(databaseT32,"管机自动化装置_images");

            Set<String> databaseT33 = new HashSet<>();  //
            databaseT33.addAll(Arrays.asList("料库系统一拖三","iTrans Tower4"));
            databasetotal.addAll(databaseT33);
            databaseall.put(databaseT33,"板材料库一拖三_images");
        }


        if (jsonArray.size() == 1){
            //只有一个
            String s1 = (String) jsonArray.get(0);
            //判断是否
            if(databasetotal.contains(s1)){
                for (Map.Entry<Set<String>, String> entry : databaseall.entrySet()) {
                    Set<String> keySet = entry.getKey();   // 获得key，进行判断
                    if (keySet.contains(s1)){
                        rs1 = redisService.get(entry.getValue());
                    }
                }
            }
        }

        //返回内容
        return rs1;
    }
}
