package com.dali.dali.local;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.net.URI;
import java.net.http.HttpRequest;
import java.util.HashMap;
import java.util.Map;

public class createwjuser {

    public static void main(String[] args) {

        // 通过表单提交参数
        String url = "http://192.168.1.225:7007/api/system/user/create";
        cn.hutool.http.HttpResponse responseht = cn.hutool.http.HttpRequest.post(url)
                .cookie("sk-token=eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyIjp7InVzZXJJZCI6IjE0NTc5OTU0ODE5NjY3NDc2NDkifSwiaWF0IjoxNzU3OTI4MDE5fQ.gUvltdmeUPlnYyXy7tF8ypMKaQv-DdGB-9LWhShty2K4cqv6zadlQkH58NjkwQTdPx7zeHRmtpQDgthTUWCE7g; Path=/; HttpOnly;")
                .body("{\"name\":\"韩镇泽\",\"username\":\"1354\",\"password\":\"123456\",\"rePassword\":\"123456\",\"status\":1}")
                .execute();
    }
}
