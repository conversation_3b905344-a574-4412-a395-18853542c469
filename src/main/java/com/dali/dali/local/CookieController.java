package com.dali.dali.local;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import java.time.Duration;
import com.fasterxml.jackson.databind.ObjectMapper;

@RestController
@RequestMapping("/cookies")
public class CookieController {

    // 设置Cookie
    @GetMapping("/set")
    public void setCookie(
            @RequestParam String name,
            @RequestParam String value,
            @RequestParam(defaultValue = "3600") int maxAge, // 过期时间，单位秒
            @RequestParam(required = false) String domain, // 指定域名，可选
            @RequestParam(required = false) String redirectUrl, // 跳转URL，可选
            HttpServletResponse response
    ) throws Exception {
        Cookie cookie = new Cookie(name, value);
        cookie.setMaxAge(maxAge);
        if (domain != null && !domain.trim().isEmpty()) {
            cookie.setDomain(domain); // 设置指定域名
        }
        // 如果不指定域名，让浏览器自动匹配当前域名
        cookie.setPath("/"); // 设置路径
        response.addCookie(cookie);
        
        // 如果指定了跳转URL，则重定向
        if (redirectUrl != null && !redirectUrl.trim().isEmpty()) {
            response.sendRedirect(redirectUrl);
        } else {
            // 如果没有指定跳转URL，返回成功信息页面
            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().write(
                "<html><body>" +
                "<h2>Cookie设置成功!</h2>" +
                "<p>Cookie名称: " + name + "</p>" +
                "<p>Cookie值: " + value + "</p>" +
                (domain != null && !domain.trim().isEmpty() ? "<p>域名: " + domain + "</p>" : "") +
                "<p>过期时间: " + maxAge + " 秒</p>" +
                "<br><a href='/cookies/list'>查看所有Cookie</a>" +
                "</body></html>"
            );
        }
    }


    /***
     * 调查问卷、单点登入
     * 用于管理员跳转，能发布调查问卷人员
     * @param response
     * @throws Exception
     */
    @GetMapping("/loginT")
    public void loginT(HttpServletRequest request1 ,HttpServletResponse response) throws Exception {
        //先获取tokenId进行单点登入鉴权
        String tokenId = request1.getParameter("tokenId");
        if (tokenId == null || tokenId.trim().isEmpty()) {
            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().write(
                "<html><body>" +
                "<h2>参数错误!</h2>" +
                "<p>缺少必需的参数，禁止登入</p>" +
                "</body></html>"
            );
            return;
        }
        //进行tokenId的验证
        // 设置请求参数
        String url = "https://oa.bodor.com:8443/portal/r/jd?cmd=com.actionsoft.apps.addons.sso_validate";
        String access_key = "2e5295e9-f46d-45e0-8de0-8a1281a6da5c";

        // 通过表单提交参数
        cn.hutool.http.HttpResponse responseht = cn.hutool.http.HttpRequest.post(url)
                .form("tokenId", tokenId)
                .form("access_key", access_key)
                .execute();

        if (responseht.isOk()) {
            String jsonStr = responseht.body();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("后台单点登入时间：" + dateFormat.format(new Date()) + "，用户：" + jsonStr);

            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            JSONObject dataObject = jsonObject.getJSONObject("data");

            String uid = dataObject.getString("uid");     //用户唯一标识、工号
            boolean validate = dataObject.getBooleanValue("validate");    //true代表该授权有效

            if (validate) {  //判断授权是否有效果

                // 创建HTTP客户端
                HttpClient client = HttpClient.newBuilder()
                        .connectTimeout(Duration.ofSeconds(10))
                        .build();

                // 准备登录数据
                Map<String, String> loginData = new HashMap<>();
                loginData.put("password", "单点登入已经鉴权了");
                loginData.put("username", uid);

                // 转换为JSON
                ObjectMapper mapper = new ObjectMapper();
                String jsonPayload = mapper.writeValueAsString(loginData);

                // 创建POST请求
                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create("http://192.168.1.225:7007/api/public/login"))
                        .header("Content-Type", "application/json")
                        .POST(HttpRequest.BodyPublishers.ofString(jsonPayload))
                        .build();

                try {
                    // 发送请求
                    HttpResponse<String> httpResponse = client.send(request, HttpResponse.BodyHandlers.ofString());

                    // 获取响应中的Set-Cookie头
                    List<String> setCookieHeaders = httpResponse.headers().allValues("Set-Cookie");

                    // 解析并设置cookie到浏览器
                    for (String cookieHeader : setCookieHeaders) {
                        // 解析cookie字符串
                        String[] cookieParts = cookieHeader.split(";");
                        String[] nameValue = cookieParts[0].split("=", 2);

                        if (nameValue.length == 2) {
                            Cookie cookie = new Cookie(nameValue[0].trim(), nameValue[1].trim());
                            cookie.setPath("/");
                            //让浏览器自动匹配
//                    cookie.setDomain("192.168.1.225");

                            // 解析其他cookie属性
                            for (int i = 1; i < cookieParts.length; i++) {
                                String part = cookieParts[i].trim();
                                if (part.toLowerCase().startsWith("max-age=")) {
                                    try {
                                        int maxAge = Integer.parseInt(part.substring(8));
                                        cookie.setMaxAge(maxAge);
                                    } catch (NumberFormatException e) {
                                        // 忽略解析错误
                                    }
                                }
                            }

                            response.addCookie(cookie);
                        }
                    }

                    // 跳转到目标页面
                    response.sendRedirect("http://192.168.1.225:7007/home");

                } catch (Exception e) {
                    // 登录失败，返回错误页面
                    response.setContentType("text/html;charset=UTF-8");
                    response.getWriter().write(
                            "<html><body>" +
                                    "<h2>登录失败!</h2>" +
                                    "<p>错误信息: " + e.getMessage() + "</p>" +
                                    "<br><a href='/cookies/loginT'>重试登录</a>" +
                                    "</body></html>"
                    );
                }

            }
        }

    }

    /***
     * 用于问卷填写的时候的单点登入，用户点击后，写入tokenId后跳转打开
     * @param response
     * @throws Exception
     */
    @GetMapping("/loginD/{surveyCode}")
    public void loginD(@PathVariable String surveyCode, HttpServletRequest request1 ,HttpServletResponse response) throws Exception{
        //先获取tokenId进行单点登入鉴权
        String tokenId = request1.getParameter("tokenId");
        if (tokenId == null || tokenId.trim().isEmpty()) {
            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().write(
                    "<html><body>" +
                            "<h2>打开问卷，跳转错误!</h2>" +
                            "<p>缺少tokenId参数</p>" +
                            "</body></html>"
            );
            return;
        }
        
        // 获取URL路径中的问卷代码
        if (surveyCode == null || surveyCode.trim().isEmpty()) {
            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().write(
                    "<html><body>" +
                            "<h2>打开问卷，跳转错误!</h2>" +
                            "<p>缺少问卷代码参数</p>" +
                            "</body></html>"
            );
            return;
        }
        
        // 记录获取到的参数
        System.out.println("获取到的问卷代码: " + surveyCode);
        System.out.println("获取到的tokenId: " + tokenId);
        //进行tokenId的验证
        // 设置请求参数
        String url = "https://oa.bodor.com:8443/portal/r/jd?cmd=com.actionsoft.apps.addons.sso_validate";
        String access_key = "2e5295e9-f46d-45e0-8de0-8a1281a6da5c";

        // 通过表单提交参数
        cn.hutool.http.HttpResponse responseht = cn.hutool.http.HttpRequest.post(url)
                .form("tokenId", tokenId)
                .form("access_key", access_key)
                .execute();

        if (responseht.isOk()) {
            String jsonStr = responseht.body();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("用户打开单点登入时间：" + dateFormat.format(new Date()) + "，用户：" + jsonStr);

            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            JSONObject dataObject = jsonObject.getJSONObject("data");

            String uid = dataObject.getString("uid");     //用户唯一标识、工号
            boolean validate = dataObject.getBooleanValue("validate");    //true代表该授权有效

            if (validate) {  //判断授权是否有效果

                // 创建HTTP客户端
                HttpClient client = HttpClient.newBuilder()
                        .connectTimeout(Duration.ofSeconds(10))
                        .build();

                // 准备登录数据
                Map<String, String> loginData = new HashMap<>();
                loginData.put("password", "单点登入已经鉴权了");
                loginData.put("username", uid);

                // 转换为JSON
                ObjectMapper mapper = new ObjectMapper();
                String jsonPayload = mapper.writeValueAsString(loginData);

                // 创建POST请求
                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create("http://192.168.1.225:7007/api/public/login"))
                        .header("Content-Type", "application/json")
                        .POST(HttpRequest.BodyPublishers.ofString(jsonPayload))
                        .build();

                try {
                    // 发送请求
                    HttpResponse<String> httpResponse = client.send(request, HttpResponse.BodyHandlers.ofString());

                    // 获取响应中的Set-Cookie头
                    List<String> setCookieHeaders = httpResponse.headers().allValues("Set-Cookie");

                    // 解析并设置cookie到浏览器
                    for (String cookieHeader : setCookieHeaders) {
                        // 解析cookie字符串
                        String[] cookieParts = cookieHeader.split(";");
                        String[] nameValue = cookieParts[0].split("=", 2);

                        if (nameValue.length == 2) {
                            Cookie cookie = new Cookie(nameValue[0].trim(), nameValue[1].trim());
                            cookie.setPath("/");
                            //让浏览器自动匹配
//                    cookie.setDomain("192.168.1.225");

                            // 解析其他cookie属性
                            for (int i = 1; i < cookieParts.length; i++) {
                                String part = cookieParts[i].trim();
                                if (part.toLowerCase().startsWith("max-age=")) {
                                    try {
                                        int maxAge = Integer.parseInt(part.substring(8));
                                        cookie.setMaxAge(maxAge);
                                    } catch (NumberFormatException e) {
                                        // 忽略解析错误
                                    }
                                }
                            }

                            response.addCookie(cookie);
                        }
                    }

                    // 跳转到目标页面，
                    // 这里和发布问卷不一样，这里是跳转到具体的问卷，具体问卷带动态参数
                    response.sendRedirect("http://192.168.1.225:7007/s/"+surveyCode);

                } catch (Exception e) {
                    // 登录失败，返回错误页面
                    response.setContentType("text/html;charset=UTF-8");
                    response.getWriter().write(
                            "<html><body>" +
                                    "<h2>打开失败，请联系管理员!</h2>" +
                                    "</body></html>"
                    );
                }

            }
        }
    }


    @GetMapping("/loginCoze")
    public void loginCoze(HttpServletRequest request1 ,HttpServletResponse response) throws Exception {

        //先获取tokenId进行单点登入鉴权
        String tokenId = request1.getParameter("tokenId");
        if (tokenId == null || tokenId.trim().isEmpty()) {
            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().write(
                    "<html><body>" +
                            "<h2>参数错误!</h2>" +
                            "<p>缺少必需的参数，禁止登入</p>" +
                            "</body></html>"
            );
            return;
        }
        //进行tokenId的验证
        // 设置请求参数
        String url = "https://oa.bodor.com:8443/portal/r/jd?cmd=com.actionsoft.apps.addons.sso_validate";
        String access_key = "2e5295e9-f46d-45e0-8de0-8a1281a6da5c";

        // 通过表单提交参数
        cn.hutool.http.HttpResponse responseht = cn.hutool.http.HttpRequest.post(url)
                .form("tokenId", tokenId)
                .form("access_key", access_key)
                .execute();

        if (responseht.isOk()) {
            String jsonStr = responseht.body();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("后台单点登入时间：" + dateFormat.format(new Date()) + "，用户：" + jsonStr);

            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            JSONObject dataObject = jsonObject.getJSONObject("data");

            String uid = dataObject.getString("uid");     //用户唯一标识、工号
            boolean validate = dataObject.getBooleanValue("validate");    //true代表该授权有效

            if (validate) {  //判断授权是否有效果

                // 创建HTTP客户端
                HttpClient client = HttpClient.newBuilder()
                        .connectTimeout(Duration.ofSeconds(10))
                        .build();

                // 准备登录数据
                Map<String, String> loginData = new HashMap<>();
                loginData.put("email", uid);
                loginData.put("password", "<EMAIL>");

                // 转换为JSON
                ObjectMapper mapper = new ObjectMapper();
                String jsonPayload = mapper.writeValueAsString(loginData);

                // 创建POST请求
                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create("http://192.168.1.225:5002/api/passport/web/email/login/"))
                        .header("Content-Type", "application/json")
                        .POST(HttpRequest.BodyPublishers.ofString(jsonPayload))
                        .build();

                try {
                    // 发送请求
                    HttpResponse<String> httpResponse = client.send(request, HttpResponse.BodyHandlers.ofString());

                    // 获取响应中的Set-Cookie头
                    List<String> setCookieHeaders = httpResponse.headers().allValues("Set-Cookie");

                    // 解析并设置cookie到浏览器
                    for (String cookieHeader : setCookieHeaders) {
                        // 解析cookie字符串
                        String[] cookieParts = cookieHeader.split(";");
                        String[] nameValue = cookieParts[0].split("=", 2);

                        if (nameValue.length == 2) {
                            Cookie cookie = new Cookie(nameValue[0].trim(), nameValue[1].trim());
                            cookie.setPath("/");
                            //让浏览器自动匹配
//                    cookie.setDomain("192.168.1.225");

                            // 解析其他cookie属性
                            for (int i = 1; i < cookieParts.length; i++) {
                                String part = cookieParts[i].trim();
                                if (part.toLowerCase().startsWith("max-age=")) {
                                    try {
                                        int maxAge = Integer.parseInt(part.substring(8));
                                        cookie.setMaxAge(maxAge);
                                    } catch (NumberFormatException e) {
                                        // 忽略解析错误
                                    }
                                }
                            }

                            response.addCookie(cookie);
                        }
                    }

                    // 跳转到目标页面
                    response.sendRedirect("http://192.168.1.225:5002");

                } catch (Exception e) {
                    // 登录失败，返回错误页面
                    response.setContentType("text/html;charset=UTF-8");
                    response.getWriter().write(
                            "<html><body>" +
                                    "<h2>登录失败!</h2>" +
                                    "<p>错误信息: " + e.getMessage() + "</p>" +
                                    "<br><a href='/cookies/loginT'>重试登录</a>" +
                                    "</body></html>"
                    );
                }

            }

        }

    }


    @GetMapping("/loginCozeY")
    public void loginCozeY(HttpServletRequest request1 ,HttpServletResponse response) throws Exception {

        //先获取tokenId进行单点登入鉴权
        String tokenId = request1.getParameter("tokenId");
        if (tokenId == null || tokenId.trim().isEmpty()) {
            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().write(
                    "<html><body>" +
                            "<h2>参数错误!</h2>" +
                            "<p>缺少必需的参数，禁止登入</p>" +
                            "</body></html>"
            );
            return;
        }
        //进行tokenId的验证
        // 设置请求参数
        String url = "https://oa.bodor.com:8443/portal/r/jd?cmd=com.actionsoft.apps.addons.sso_validate";
        String access_key = "2e5295e9-f46d-45e0-8de0-8a1281a6da5c";

        // 通过表单提交参数
        cn.hutool.http.HttpResponse responseht = cn.hutool.http.HttpRequest.post(url)
                .form("tokenId", tokenId)
                .form("access_key", access_key)
                .execute();

        if (responseht.isOk()) {
            String jsonStr = responseht.body();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            System.out.println("后台单点登入时间：" + dateFormat.format(new Date()) + "，用户：" + jsonStr);

            JSONObject jsonObject = JSONObject.parseObject(jsonStr);
            JSONObject dataObject = jsonObject.getJSONObject("data");

            String uid = dataObject.getString("uid");     //用户唯一标识、工号
            boolean validate = dataObject.getBooleanValue("validate");    //true代表该授权有效

            if (validate) {  //判断授权是否有效果

                // 创建HTTP客户端
                HttpClient client = HttpClient.newBuilder()
                        .connectTimeout(Duration.ofSeconds(10))
                        .build();

                // 准备登录数据
                Map<String, String> loginData = new HashMap<>();
                loginData.put("email", uid);
                loginData.put("password", "<EMAIL>");

                // 转换为JSON
                ObjectMapper mapper = new ObjectMapper();
                String jsonPayload = mapper.writeValueAsString(loginData);

                // 创建POST请求   https://ai-api.bodor.com
                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create("http://ai-api.bodor.com:5002/api/passport/web/email/login/"))
                        .header("Content-Type", "application/json")
                        .POST(HttpRequest.BodyPublishers.ofString(jsonPayload))
                        .build();

                try {
                    // 发送请求
                    HttpResponse<String> httpResponse = client.send(request, HttpResponse.BodyHandlers.ofString());

                    // 获取响应中的Set-Cookie头
                    List<String> setCookieHeaders = httpResponse.headers().allValues("Set-Cookie");

                    // 解析并设置cookie到浏览器
                    for (String cookieHeader : setCookieHeaders) {
                        // 解析cookie字符串
                        String[] cookieParts = cookieHeader.split(";");
                        String[] nameValue = cookieParts[0].split("=", 2);

                        if (nameValue.length == 2) {
                            Cookie cookie = new Cookie(nameValue[0].trim(), nameValue[1].trim());
                            cookie.setPath("/");
                            //让浏览器自动匹配
//                    cookie.setDomain("192.168.1.225");

                            // 解析其他cookie属性
                            for (int i = 1; i < cookieParts.length; i++) {
                                String part = cookieParts[i].trim();
                                if (part.toLowerCase().startsWith("max-age=")) {
                                    try {
                                        int maxAge = Integer.parseInt(part.substring(8));
                                        cookie.setMaxAge(maxAge);
                                    } catch (NumberFormatException e) {
                                        // 忽略解析错误
                                    }
                                }
                            }

                            response.addCookie(cookie);
                        }
                    }

                    // 跳转到目标页面
                    response.sendRedirect("http://ai-api.bodor.com:5002");

                } catch (Exception e) {
                    // 登录失败，返回错误页面
                    response.setContentType("text/html;charset=UTF-8");
                    response.getWriter().write(
                            "<html><body>" +
                                    "<h2>登录失败!</h2>" +
                                    "<p>错误信息: " + e.getMessage() + "</p>" +
                                    "<br><a href='/cookies/loginT'>重试登录</a>" +
                                    "</body></html>"
                    );
                }

            }

        }

    }


    // 获取指定名称的Cookie
    @GetMapping("/get/{name}")
    public String getCookie(
            @PathVariable String name,
            HttpServletRequest request
    ) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (name.equals(cookie.getName())) {
                    return "Cookie found: " + name + "=" + cookie.getValue();
                }
            }
        }
        return "Cookie not found: " + name;
    }

    // 删除指定名称的Cookie
    @GetMapping("/delete/{name}")
    public String deleteCookie(
            @PathVariable String name,
            HttpServletRequest request,
            HttpServletResponse response
    ) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            Optional<Cookie> optionalCookie = java.util.Arrays.stream(cookies)
                    .filter(c -> name.equals(c.getName()))
                    .findFirst();
            if (optionalCookie.isPresent()) {
                Cookie deleteCookie = new Cookie(name, null);
                deleteCookie.setMaxAge(0);
                deleteCookie.setPath("/");
                response.addCookie(deleteCookie);
                return "Cookie deleted successfully: " + name;
            }
        }
        return "Cookie not found for deletion: " + name;
    }

    // 获取所有Cookie
    @GetMapping("/list")
    public String listAllCookies(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null && cookies.length > 0) {
            StringBuilder sb = new StringBuilder("All cookies:\n");
            for (Cookie cookie : cookies) {
                sb.append(cookie.getName()).append("=").append(cookie.getValue()).append("\n");
            }
            return sb.toString();
        }
        return "No cookies found";
    }
}