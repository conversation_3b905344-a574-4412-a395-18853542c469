package com.dali.dali.local;

import java.sql.*;
import java.util.*;

public class cozeController {


    public static void main(String[] args) {


        //============================查找============================
//        List<Map<String, Object>> allUsers = new cozeController().executeQuery("aqyy", "SELECT * FROM BPM_ZX_USER");
//        System.out.println("所有用户: " + allUsers);

//        List<Map<String, Object>> allUsers = new cozeController().executeQuery("coze", "SELECT * FROM user");
//        System.out.println("所有用户: " + allUsers);


        //============================增加============================
//        // 创建用户数据
//        Map<String, Object> userData = new HashMap<>();
//        userData.put("id", 12652);
//        userData.put("name", "苏彬");
//        userData.put("unique_name", "012650");
//        userData.put("email", "012650");
//        userData.put("password", "$argon2id$v=19$m=65536,t=3,p=4$W7ovqeu9vzMBUcxDLGuH2g$ChT/SnooYAFyr18QdF7DLEq23cFSTiyedAdpKIRLvxg");
//        userData.put("icon_uri", "default_icon/user_default_icon.png");
//        userData.put("user_verified", "0");
//        userData.put("locale", "zh-CN");
//
//        // 插入到db1数据库的users表
//        int result = new cozeController().insert("coze", "user", userData);
//        System.out.println("插入用户结果，影响行数: " + result);


        //============================更新============================
//        Map<String, Object> updateData = new HashMap<>();
//        updateData.put("deleted_at", 1);  //设置物理删除
//        int updateResult = new cozeController().update("coze", "user", updateData, "email = ?", "012652");
//        System.out.println("更新结果: " + updateResult);

        cozeController cozeController = new cozeController();
        //1、首先查找出  信息安全运营系统中所有工号
        List<Map<String, Object>> allUsersaqyy =cozeController.executeQuery("aqyy", "SELECT uid,userName FROM BPM_ZX_USER");
        System.out.println("所有用户aqyy: " + allUsersaqyy);
        //获得 信息安全运营系统中的全部工号，获取工号和姓名对照，用于添加时查找英明
        Map<String,String> aqyyMap = new HashMap<>();
        Set<String> aqyySet = new HashSet<>();
        for (Map<String, Object> map: allUsersaqyy) {
            aqyySet.add((String) map.get("uid"));
            aqyyMap.put((String) map.get("uid"),(String) map.get("userName"));
        }

        List<Map<String, Object>> allUserscoze =cozeController.executeQuery("coze", "SELECT email FROM user");
        System.out.println("所有用户coze: " + allUserscoze);
        //获得 coze中的全部可登入账户
        Set<String> cozeSet = new HashSet<>();
        for (Map<String, Object> map: allUserscoze) {
            cozeSet.add((String) map.get("email"));
        }


        Set<String> setA = aqyySet;
        Set<String> setB = cozeSet;

        // A：aqyySet 中有，但B：cozeSet 中没有的元素，新入职的人、需要添加
        Set<String> onlyInA = new HashSet<>(setA);
        onlyInA.removeAll(setB);
        System.out.println("A中有B中没有的: " + onlyInA); // [Banana, Orange]
        //添加新入职人员到coze中
        List<Map<String, Object>> mapuser = new ArrayList<>();
        for (String addu: onlyInA) {
            Map mapT = new HashMap<>();
            mapT.put("uid",addu);
            mapT.put("userName",aqyyMap.get(addu));
            mapuser.add(mapT);
        }
        //添加新员工到coze中
        cozeController.initcsh(mapuser,cozeController);


        // B：cozeSet 中有，但A：aqyySet 中没有的元素，离职人员、需要物理删除
        Set<String> onlyInB = new HashSet<>(setB);
        onlyInB.removeAll(setA);
        System.out.println("B中有A中没有的: " + onlyInB); // [Mango]

        for (String email: onlyInB) {
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("deleted_at", 1);  //设置物理删除
            int updateResult = cozeController.update("coze", "user", updateData, "email = ?", email);
            System.out.println("更新结果: " + updateResult);
        }


    }

    /***
     * 初始化coze全部人员
     * @param allUsers
     * @param cozeController
     */
    public void initcsh(List<Map<String, Object>> allUsers,cozeController cozeController){
        //进行遍历存储
        for (Map map: allUsers) {
            //调用单个人员添加
            adduserTocoze(map,cozeController);
        }
    }

    /***
     * 传入map中的人员，进行添加
     */
    public void adduserTocoze(Map map,cozeController cozeController){
        try {
            //2、插入 user表
            Map<String, Object> userData = new HashMap<>();
            userData.put("id", convertStringToNumber((String) map.get("uid")));
            userData.put("name", (String) map.get("userName"));
            userData.put("unique_name", (String) map.get("uid"));
            userData.put("email", (String) map.get("uid"));
            userData.put("password", "$argon2id$v=19$m=65536,t=3,p=4$W7ovqeu9vzMBUcxDLGuH2g$ChT/SnooYAFyr18QdF7DLEq23cFSTiyedAdpKIRLvxg");
            userData.put("icon_uri", "default_icon/user_default_icon.png");
            userData.put("user_verified", "0");
            userData.put("locale", "zh-CN");
            cozeController.insert("coze", "user", userData);

            //3、插入 space表
            Map<String, Object> spaceData = new HashMap<>();
            spaceData.put("id", convertStringToNumber((String) map.get("uid")));
            spaceData.put("owner_id", convertStringToNumber((String) map.get("uid")));
            spaceData.put("name", "Personal Space");
            spaceData.put("description", "This is your personal space");
            spaceData.put("icon_uri", "default_icon/team_default_icon.png");
            spaceData.put("creator_id", convertStringToNumber((String) map.get("uid")));
            cozeController.insert("coze", "space", spaceData);

            //4、插入 space_user表
            Map<String, Object> space_userData = new HashMap<>();
            space_userData.put("space_id", convertStringToNumber((String) map.get("uid")));
            space_userData.put("user_id", convertStringToNumber((String) map.get("uid")));
            space_userData.put("role_type", 1);
            cozeController.insert("coze", "space_user", space_userData);


        }catch (Exception exception){

            System.out.println("工号："+ map.get("uid")+"，姓名："+ map.get("userName")+"。已经添加");
        }
    }

    /**
     * 将字符串转换为数字或12位数字编码
     * 纯数字字符串：去除前导零
     * 带字母字符串：转换为12位数字编码
     * @param str 输入的字符串，如"012652"、"0018"、"IN003"、"VN090"
     * @return 转换后的数字或12位编码
     */
    public static long convertStringToNumber(String str) {
        if (str == null || str.isEmpty()) {
            throw new IllegalArgumentException("输入字符串不能为空");
        }

        str = str.trim().toUpperCase();

        // 检查是否为纯数字字符串
        if (str.matches("^\\d+$")) {
            // 纯数字字符串，去除前导零
            return Long.parseLong(str);
        } else {
            // 包含字母的字符串，转换为12位数字编码
            return convertAlphanumericTo12Digits(str);
        }
    }

    /**
     * 将包含字母的字符串转换为12位数字编码
     * 使用哈希算法确保同一字符串多次转换结果相同
     * @param str 包含字母的字符串
     * @return 12位数字编码
     */
    private static long convertAlphanumericTo12Digits(String str) {
        // 使用字符串的hashCode作为基础
        long hash = str.hashCode();

        // 确保为正数
        if (hash < 0) {
            hash = -hash;
        }

        // 将字符串转换为数字表示
        StringBuilder numericStr = new StringBuilder();

        for (char c : str.toCharArray()) {
            if (Character.isLetter(c)) {
                // 字母转换为两位数字：A=10, B=11, ..., Z=35
                int letterValue = c - 'A' + 10;
                numericStr.append(String.format("%02d", letterValue));
            } else if (Character.isDigit(c)) {
                // 数字直接添加，前面补0变成两位
                numericStr.append(String.format("%02d", c - '0'));
            }
        }

        // 结合hash值和字符转换值
        String combined = hash + numericStr.toString();

        // 如果长度超过12位，取前12位
        if (combined.length() > 12) {
            combined = combined.substring(0, 12);
        } else if (combined.length() < 12) {
            // 如果不足12位，用hash值的后几位补齐
            String hashStr = String.valueOf(hash);
            while (combined.length() < 12) {
                for (int i = 0; i < hashStr.length() && combined.length() < 12; i++) {
                    combined += hashStr.charAt(i);
                }
            }
        }

        return Long.parseLong(combined);
    }



    // 数据库连接配置
    private static final Map<String, DatabaseConfig> DATABASE_CONFIGS = new HashMap<>();
    
    static {
        // 配置数据库1
        DATABASE_CONFIGS.put("aqyy", new DatabaseConfig(
            "*******************************************************************************************",
            "root",
            "P6fZ3@yQ8uLkT"
        ));
        
        // 配置数据库2
        DATABASE_CONFIGS.put("coze", new DatabaseConfig(
            "************************************************************************************************",
            "root",
            "root"
        ));
        
        // 可以继续添加更多数据库配置
    }
    
    /**
     * 数据库配置类
     */
    private static class DatabaseConfig {
        private final String url;
        private final String username;
        private final String password;
        
        public DatabaseConfig(String url, String username, String password) {
            this.url = url;
            this.username = username;
            this.password = password;
        }
        
        public String getUrl() { return url; }
        public String getUsername() { return username; }
        public String getPassword() { return password; }
    }
    
    /**
     * 获取指定数据库的连接
     * @param dbKey 数据库标识
     * @return Connection对象
     */
    public Connection getConnection(String dbKey) throws SQLException {
        DatabaseConfig config = DATABASE_CONFIGS.get(dbKey);
        if (config == null) {
            throw new IllegalArgumentException("未找到数据库配置: " + dbKey);
        }
        
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            return DriverManager.getConnection(config.getUrl(), config.getUsername(), config.getPassword());
        } catch (ClassNotFoundException e) {
            throw new SQLException("MySQL驱动未找到", e);
        }
    }


    /**
     * 执行查询操作
     * @param dbKey 数据库标识
     * @param sql SQL查询语句
     * @param params 参数
     * @return 查询结果列表
     */
    public List<Map<String, Object>> executeQuery(String dbKey, String sql, Object... params) {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try (Connection conn = getConnection(dbKey);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            // 设置参数
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }
            
            try (ResultSet rs = pstmt.executeQuery()) {
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();
                
                while (rs.next()) {
                    Map<String, Object> row = new HashMap<>();
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value = rs.getObject(i);
                        row.put(columnName, value);
                    }
                    results.add(row);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
            throw new RuntimeException("查询执行失败: " + e.getMessage(), e);
        }
        
        return results;
    }
    
    /**
     * 执行更新操作（INSERT、UPDATE、DELETE）
     * @param dbKey 数据库标识
     * @param sql SQL语句
     * @param params 参数
     * @return 影响的行数
     */
    public int executeUpdate(String dbKey, String sql, Object... params) {
        try (Connection conn = getConnection(dbKey);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            // 设置参数
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }
            
            return pstmt.executeUpdate();
            
        } catch (SQLException e) {
            e.printStackTrace();
            throw new RuntimeException("更新执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 插入数据
     * @param dbKey 数据库标识
     * @param tableName 表名
     * @param data 数据Map，key为字段名，value为字段值
     * @return 影响的行数
     */
    public int insert(String dbKey, String tableName, Map<String, Object> data) {
        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("插入数据不能为空");
        }
        
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(tableName).append(" (");
        
        StringBuilder values = new StringBuilder(" VALUES (");
        List<Object> params = new ArrayList<>();
        
        boolean first = true;
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            if (!first) {
                sql.append(", ");
                values.append(", ");
            }
            sql.append(entry.getKey());
            values.append("?");
            params.add(entry.getValue());
            first = false;
        }
        
        sql.append(")").append(values).append(")");
        
        return executeUpdate(dbKey, sql.toString(), params.toArray());
    }
    
    /**
     * 更新数据
     * @param dbKey 数据库标识
     * @param tableName 表名
     * @param data 更新的数据
     * @param whereClause WHERE条件
     * @param whereParams WHERE条件参数
     * @return 影响的行数
     */
    public int update(String dbKey, String tableName, Map<String, Object> data, String whereClause, Object... whereParams) {
        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("更新数据不能为空");
        }
        
        StringBuilder sql = new StringBuilder("UPDATE ");
        sql.append(tableName).append(" SET ");
        
        List<Object> params = new ArrayList<>();
        boolean first = true;
        
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            if (!first) {
                sql.append(", ");
            }
            sql.append(entry.getKey()).append(" = ?");
            params.add(entry.getValue());
            first = false;
        }
        
        if (whereClause != null && !whereClause.trim().isEmpty()) {
            sql.append(" WHERE ").append(whereClause);
            params.addAll(Arrays.asList(whereParams));
        }
        
        return executeUpdate(dbKey, sql.toString(), params.toArray());
    }
    
    /**
     * 删除数据
     * @param dbKey 数据库标识
     * @param tableName 表名
     * @param whereClause WHERE条件
     * @param whereParams WHERE条件参数
     * @return 影响的行数
     */
    public int delete(String dbKey, String tableName, String whereClause, Object... whereParams) {
        StringBuilder sql = new StringBuilder("DELETE FROM ");
        sql.append(tableName);
        
        List<Object> params = new ArrayList<>();
        
        if (whereClause != null && !whereClause.trim().isEmpty()) {
            sql.append(" WHERE ").append(whereClause);
            params.addAll(Arrays.asList(whereParams));
        }
        
        return executeUpdate(dbKey, sql.toString(), params.toArray());
    }
    
    /**
     * 批量执行SQL
     * @param dbKey 数据库标识
     * @param sqls SQL语句列表
     * @return 每个SQL影响的行数数组
     */
    public int[] executeBatch(String dbKey, List<String> sqls) {
        try (Connection conn = getConnection(dbKey);
             Statement stmt = conn.createStatement()) {
            
            conn.setAutoCommit(false);
            
            for (String sql : sqls) {
                stmt.addBatch(sql);
            }
            
            int[] results = stmt.executeBatch();
            conn.commit();
            
            return results;
            
        } catch (SQLException e) {
            e.printStackTrace();
            throw new RuntimeException("批量执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 测试数据库连接
     * @param dbKey 数据库标识
     * @return 连接是否成功
     */
    public boolean testConnection(String dbKey) {
        try (Connection conn = getConnection(dbKey)) {
            return conn != null && !conn.isClosed();
        } catch (SQLException e) {
            System.err.println("数据库连接测试失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 使用示例方法
     */
    public void exampleUsage() {
        try {
            // 测试连接
            System.out.println("数据库1连接测试: " + testConnection("db1"));
            System.out.println("数据库2连接测试: " + testConnection("db2"));
            
            // 插入数据示例
            Map<String, Object> userData = new HashMap<>();
            userData.put("name", "张三");
            userData.put("age", 25);
            userData.put("email", "<EMAIL>");
            int insertResult = insert("db1", "users", userData);
            System.out.println("插入结果: " + insertResult);
            
            // 查询数据示例
            List<Map<String, Object>> users = executeQuery("db1", "SELECT * FROM users WHERE age > ?", 20);
            System.out.println("查询结果: " + users);
            
            // 更新数据示例
            Map<String, Object> updateData = new HashMap<>();
            updateData.put("age", 26);
            int updateResult = update("db1", "users", updateData, "name = ?", "张三");
            System.out.println("更新结果: " + updateResult);
            
            // 删除数据示例
            int deleteResult = delete("db1", "users", "age > ?", 30);
            System.out.println("删除结果: " + deleteResult);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
