package com.dali.dali.local;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/***
 * 信息安全运营系统访问该中转系统，
 * 该系统再去访问缓存系统，模仿postman发送请求
 * 缓存系统再去访问，本地的信息安全运营系统获取数据
 * vpn的地址必须是 20.1.1.2
 * 后期可以进行改造，使用推送的方式，这样不用再需要要求vpn的地址了
 */
@RestController
@RequestMapping("/zzAIqx")
@CrossOrigin
public class zzAIqxController {


    //0.1、全部人员工号，用于加密验证，<jobnumber,username>
    @GetMapping("/updaAlljobnumber")
    public void updateJobAndNane(){
        HttpResponse response = HttpRequest.get("http://10.11.2.160:4000/updaAlljobnumber")
                .header("Content-Type", "application/json")
                .execute();
    }

    //0.2、全部人员对应部门全路径id <jobnumber,deptpathid>用于登入验证
    @GetMapping("/updaAlljobToid")
    public void updateJobAndDeptid(){
        HttpResponse response = HttpRequest.get("http://10.11.2.160:4000/updaAlljobToid")
                .header("Content-Type", "application/json")
                .execute();
    }




    //1、全部可登入人员  对应工号 和 权限 <jobnumber,qx>用于登入验证
    @GetMapping("/updaAlljobToQx")
    public void updateJobToQx(){
        HttpResponse response = HttpRequest.get("http://10.11.2.160:4000/updaAlljobToQx")
                .header("Content-Type", "application/json")
                .execute();
    }

    //2、全部可登入人员 对应部门id 和 权限 <deptid,qx>用于登入验证
    @GetMapping("/updaAlldeptIdToQx")
    public void updateDeptToQx(){
        HttpResponse response = HttpRequest.get("http://10.11.2.160:4000/updaAlldeptIdToQx")
                .header("Content-Type", "application/json")
                .execute();
    }





    //3、对应工号 和 权限 <knowbase,<jobnumber,qx>>用于知识库验证
    @GetMapping("/updaAllKbJobToQx")
    public void updatebaseToJobAndQx(){
        HttpResponse response = HttpRequest.get("http://10.11.2.160:4000/updaAllKbJobToQx")
                .header("Content-Type", "application/json")
                .execute();
    }

    //4、对应部门id 和 权限 <knowbase,<deptid,qx>>用于知识库验证     AllKbDeptToQx
    @GetMapping("/updaAllKbDeptToQx")
    public void updatebaseToDeptAndQx(){
        HttpResponse response = HttpRequest.get("http://10.11.2.160:4000/updaAllKbDeptToQx")
                .header("Content-Type", "application/json")
                .execute();
    }


    //5、版本更新，进行缓存List[<Map>]用于知识库验证     AllKbDeptToQx
    @GetMapping("/updaAllVersionMessage")
    public void updaAllVersionMessage(){
        HttpResponse response = HttpRequest.get("http://10.11.2.160:4000/updaAllVersionMessage")
                .header("Content-Type", "application/json")
                .execute();
    }

    //6、版本更新，进行缓存List[<Map>]用于知识库验证     AllKbDeptToQx
    @GetMapping("/updaAiMessage")
    public void updaAiMessage(){
        HttpResponse response = HttpRequest.get("http://10.11.2.160:4000/updaAiMessage")
                .header("Content-Type", "application/json")
                .execute();
    }

}
