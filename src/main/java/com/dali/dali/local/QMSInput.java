package com.dali.dali.local;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import com.alibaba.fastjson.JSON;
import okhttp3.*;

import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class QMSInput {

    /**
     * 读取Excel文件并将数据转换为List<Map>格式
     * @param filePath Excel文件路径
     * @return List<Map<String, Object>> 每一行数据作为一个Map，所有行组成List
     */
    public List<Map<String, Object>> readExcelToListMap(String filePath) {
        List<Map<String, Object>> dataList = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 获取表头行
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                System.out.println("Excel文件为空或没有表头");
                return dataList;
            }

            // 读取表头
            List<String> headers = new ArrayList<>();
            for (Cell cell : headerRow) {
                headers.add(getCellValueAsString(cell));
            }

            // 验证表头是否包含期望的列
            validateHeaders(headers);

            // 读取数据行
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    continue; // 跳过空行
                }

                Map<String, Object> rowData = new HashMap<>();

                // 遍历每一列
                for (int cellIndex = 0; cellIndex < headers.size(); cellIndex++) {
                    Cell cell = row.getCell(cellIndex);
                    String columnName = headers.get(cellIndex);
                    Object cellValue = getCellValue(cell);
                    rowData.put(columnName, cellValue);
                }

                dataList.add(rowData);
            }

            System.out.println("成功读取Excel文件，共" + dataList.size() + "行数据");

        } catch (IOException e) {
            System.err.println("读取Excel文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("处理Excel文件时发生未知错误: " + e.getMessage());
            e.printStackTrace();
        }

        return dataList;
    }

    /**
     * 验证表头是否包含期望的列
     * @param headers 表头列表
     */
    private void validateHeaders(List<String> headers) {
        List<String> expectedHeaders = Arrays.asList("ip_address", "login_time", "user_name", "login_name");

        for (String expectedHeader : expectedHeaders) {
            if (!headers.contains(expectedHeader)) {
                System.out.println("警告: 未找到期望的列 '" + expectedHeader + "'");
            }
        }

        System.out.println("实际表头: " + headers);
    }

    /**
     * 获取单元格的值
     * @param cell 单元格
     * @return 单元格的值
     */
    private Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    // 如果是日期格式，返回格式化的日期字符串
                    Date date = cell.getDateCellValue();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return sdf.format(date);
                } else {
                    // 如果是数字，检查是否为整数
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return (long) numericValue;
                    } else {
                        return numericValue;
                    }
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return null;
            default:
                return cell.toString();
        }
    }

    /**
     * 获取单元格的值作为字符串（主要用于表头）
     * @param cell 单元格
     * @return 单元格的字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    Date date = cell.getDateCellValue();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return sdf.format(date);
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            default:
                return cell.toString().trim();
        }
    }

    /**
     * 将Excel数据转换为请求体格式
     * @param dataList Excel读取的数据
     * @return 转换后的请求体Map
     */
    public Map<String, Object> convertToRequestBody(List<Map<String, Object>> dataList) {
        Map<String, Object> requestBody = new HashMap<>();

        // 获取时间范围
        String[] timeRange = calculateTimeRange(dataList);
        requestBody.put("startTime", timeRange[0]);
        requestBody.put("endTime", timeRange[1]);

        // 转换message数据
        List<Map<String, Object>> messageList = new ArrayList<>();
        for (Map<String, Object> row : dataList) {
            Map<String, Object> message = new HashMap<>();

            // 映射字段
            message.put("logTime", row.get("login_time"));
            message.put("serviceName", "QMS");
            message.put("jobNumber", row.get("login_name"));
            message.put("username", row.get("user_name"));
            message.put("ip", row.get("ip_address"));
            message.put("action", "登入");
            message.put("describe", "用户登入");

            messageList.add(message);
        }

        requestBody.put("message", messageList);
        requestBody.put("timeStamp", String.valueOf(System.currentTimeMillis()));
        requestBody.put("abstract", "QmsKeyLog");

        return requestBody;
    }

    /**
     * 计算时间范围
     * @param dataList 数据列表
     * @return 时间范围数组 [startTime, endTime]
     */
    private String[] calculateTimeRange(List<Map<String, Object>> dataList) {
        if (dataList.isEmpty()) {
            String currentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            return new String[]{currentTime, currentTime};
        }

        String minTime = null;
        String maxTime = null;

        for (Map<String, Object> row : dataList) {
            String loginTime = (String) row.get("login_time");
            if (loginTime != null && !loginTime.trim().isEmpty()) {
                if (minTime == null || loginTime.compareTo(minTime) < 0) {
                    minTime = loginTime;
                }
                if (maxTime == null || loginTime.compareTo(maxTime) > 0) {
                    maxTime = loginTime;
                }
            }
        }

        // 如果没有找到有效时间，使用当前时间
        if (minTime == null || maxTime == null) {
            String currentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            return new String[]{currentTime, currentTime};
        }

        return new String[]{minTime, maxTime};
    }

    /**
     * 发送POST请求
     * @param requestBody 请求体
     * @param url 请求地址
     * @return 响应结果
     */
    public String sendPostRequest(Map<String, Object> requestBody, String url) {
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();

        try {
            // 将Map转换为JSON字符串
            String jsonBody = JSON.toJSONString(requestBody);
            System.out.println("发送的JSON请求体:");
            System.out.println(jsonBody);

            // 创建请求体
            RequestBody body = RequestBody.create(
                    jsonBody,
                    MediaType.parse("application/json; charset=utf-8")
            );

            // 创建请求
            Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 发送请求
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";

                System.out.println("响应状态码: " + response.code());
                System.out.println("响应内容: " + responseBody);

                if (response.isSuccessful()) {
                    System.out.println("请求发送成功!");
                } else {
                    System.err.println("请求发送失败，状态码: " + response.code());
                }

                return responseBody;
            }

        } catch (IOException e) {
            System.err.println("发送POST请求时发生错误: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 主方法，用于测试功能
     */
    public static void main(String[] args) {
        QMSInput qmsInput = new QMSInput();
        String filePath = "C:\\Users\\<USER>\\Desktop\\acs_login_log.xlsx";

        // 1. 读取Excel文件
        List<Map<String, Object>> dataList = qmsInput.readExcelToListMap(filePath);


        // 2. 转换数据格式
        Map<String, Object> requestBody = qmsInput.convertToRequestBody(dataList);

        System.out.println("\n=== 转换后的请求体格式 ===");
        System.out.println("startTime: " + requestBody.get("startTime"));
        System.out.println("endTime: " + requestBody.get("endTime"));
        System.out.println("timeStamp: " + requestBody.get("timeStamp"));
        System.out.println("abstract: " + requestBody.get("abstract"));

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> messageList = (List<Map<String, Object>>) requestBody.get("message");
        System.out.println("message数组长度: " + messageList.size());

        // 显示前几条message数据
        for (int i = 0; i < Math.min(2, messageList.size()); i++) {
            System.out.println("message[" + i + "]: " + messageList.get(i));
        }

        // 3. 发送POST请求
        String url = "http://192.168.1.116:8090/put/Qms/log";
        System.out.println("\n=== 发送POST请求 ===");
        System.out.println("请求地址: " + url);

        String response = qmsInput.sendPostRequest(requestBody, url);

        if (response != null) {
            System.out.println("\n=== 请求完成 ===");
            System.out.println("共处理 " + dataList.size() + " 条数据");
        } else {
            System.err.println("\n=== 请求失败 ===");
        }
    }
}
