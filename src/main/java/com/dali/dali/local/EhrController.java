package com.dali.dali.local;

import com.dali.dali.until.mysqlDB;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public class EhrController {

    public static void main(String[] args) throws SQLException {
        //获取数据库连接
        Connection bodoraiDB = mysqlDB.getConnection("***********", 3305, "root", "P6fZ3@yQ8uLkT", "bodorai");
        //查找
        List<Map<String, Object>> maps = mysqlDB.executeQuery(bodoraiDB, "SELECT * FROM job_description");
        System.out.println(maps);

        //添加
        mysqlDB.executeInsertWithDebug(bodoraiDB,
                "INSERT INTO job_description (jobId, jobCode,jobTitle,jobStatus,kind,category" +
                        "duty,`require`,createDayTime,createJobNumber,updateDayTime,updateJobNumber) VALUES " +
                        "(?,?,?,?,?,?,?,?,?,?,?,?)",
                "22c27689-9e08-4c5d-9f8f-a5a2d61a2967", "13632","采购工程师（光学）",
                "招聘中", "全职", "校园招聘",
                "1、采购计划与执行：依据生产计划和库存状况，管理采购订单，保障生产所需物资按时供应。负责新品物料采购，依据物料属性寻找合适厂家，议价后完成物料核价与订单下达工作。\\r\\n2、供应商开发与管理：开发新供方，开展供方评审与引入工作。管理供应商物料交付、成本、质量及合作关系，进行供应商等级评定，维护良好合作，确保供应稳定。\\r\\n3、成本与质量把控：通过谈判、比价等手段，有效控制采购成本；严格把控采购物资质量，协同质检处理不合格品。\\r\\n4、项目协作：积极参与公司新项目/降本项目，与研发及产品中心团队紧密配合，及时寻找优质方案与供方，助力项目顺利推进。\\r\\n5、其他工作：完成上级领导安排的其他工作任务，确保各项采购相关事务高效完成", "1、本科及以上学历，光学/光电等相关专业；\\r\\n2、学科知识\u200C：掌握几何光学、物理光学等基础理论，熟悉透镜、激光器、光通信器件等光学元件的性能参数与工艺流程；加分项：了解光学冷加工、半导体光电器件或光学系统；\\r\\n3、综合素质要求：\\r\\n熟练使用办公软件，具备一定的数据分析能力，良好的沟通与执行力；有驾驶证，会开车；英语水平良好，6级及以上优先考虑。",
                "2025-08-22T11:31:03", "000000", "2025-09-30T15:18:45","000000"
                );

        //关闭数据库连接
        mysqlDB.closeConnection(bodoraiDB);
    }
}
