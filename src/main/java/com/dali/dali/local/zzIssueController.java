package com.dali.dali.local;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.dali.dali.until.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/***
 * 用于取远程redis中的
 * 以前ibodor公众号中的缓存数据获得，现在已经废弃，不再使用
 */
//@RestController
//@EnableScheduling // 启用定时任务
public class zzIssueController {

    @Autowired
    RedisService redisService;

    @GetMapping("tongRomteAndLocal")
    // 每天早上2点执行
//    @Scheduled(cron = "0 0 2 * * ?")
    public void tongRomteAndLocal(){
        List<Map<String, String>> allDataAndDelete = redisService.getAllDataAndDelete();

        HttpResponse response = HttpRequest.post("http://192.168.1.116:8090/qx/issue/comeIssueAll")
                .body(JSON.toJSONString(allDataAndDelete))
                .execute();

        for (Map<String, String> map:allDataAndDelete) {
            System.out.println(map);
        }
    }

}
