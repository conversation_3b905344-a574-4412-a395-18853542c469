package com.dali.dali.until;

import org.apache.commons.codec.binary.Base32;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class SymmetricEncryptionExample {

    // 密钥（16字节 = 128位，适用于AES-128）
    private static final String SECRET_KEY = "bodor20240701@@@";
    private static final String INIT_VECTOR = "InitializationVe"; // 16 bytes IV

    public static String encryptToBase32(String value) throws Exception {
        IvParameterSpec iv = new IvParameterSpec(INIT_VECTOR.getBytes("UTF-8"));
        SecretKeySpec skeySpec = new SecretKeySpec(SECRET_KEY.getBytes("UTF-8"), "AES");

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);

        byte[] encrypted = cipher.doFinal(value.getBytes("UTF-8"));

        Base32 base32 = new Base32();
        return new String(base32.encode(encrypted));
    }

    public static String decryptFromBase32(String encoded) throws Exception {
        IvParameterSpec iv = new IvParameterSpec(INIT_VECTOR.getBytes("UTF-8"));
        SecretKeySpec skeySpec = new SecretKeySpec(SECRET_KEY.getBytes("UTF-8"), "AES");

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);

        Base32 base32 = new Base32();
        byte[] encryptedBytes = base32.decode(encoded.getBytes());

        byte[] original = cipher.doFinal(encryptedBytes);
        return new String(original, "UTF-8");
    }

    public static void main(String[] args) throws Exception {
        String originalText = "苏二川__012652__2025-06-12";
        System.out.println("Original: " + originalText);

        String encryptedText = encryptToBase32(originalText);
        System.out.println("加密内容: " + encryptedText);

        String decryptedText = decryptFromBase32(encryptedText);
        System.out.println("解密内容: " + decryptedText);
    }
}