package com.dali.dali.until;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/***
 * 封装的设置到期时间
 */
@Service
public class RedisService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    
    // 用于操作Redis 1号数据库的模板
    private StringRedisTemplate db1StringRedisTemplate;
    
    // 用于操作Redis 2号数据库的模板
    private StringRedisTemplate db2StringRedisTemplate;

    /**
     * 在 Redis 中创建定时字符串
     * @param key 键
     * @param value 值
     * @param timeout 过期时间
     * @param timeUnit 时间单位
     */
    public void setWithExpiration(String key, String value, long timeout, TimeUnit timeUnit) {
        stringRedisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    /**
     * 根据键获取值
     * @param key 键
     * @return 值
     */
    public String get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }



    /**
     * 在 Redis 中创建一个 Map 对象（哈希）
     * @param key 哈希的键
     * @param map 要存储的键值对 Map
     */
    public void createMap(String key, Map<String, String> map) {
        HashOperations<String, String, String> hashOps = stringRedisTemplate.opsForHash();
        // 将 Map 中的键值对存储到 Redis 的哈希中
        hashOps.putAll(key, map);
    }

    /**
     * 删除 Redis 中的一个 Map 对象（哈希）
     * @param key 哈希的键
     */
    public void deleteMap(String key) {
        stringRedisTemplate.delete(key);
    }

    /**
     * 获取 Redis 中指定哈希键的所有键值对
     * @param key 哈希的键
     * @return 哈希中的键值对 Map
     */
    public Map<String, String> getMap(String key) {
        HashOperations<String, String, String> hashOps = stringRedisTemplate.opsForHash();
        return hashOps.entries(key);
    }


    /***
     * 1、先获取全部的key
     * 2、根据key获取内容
     * 3、删除全部key的内容
     * 4、返回获取的内容
     * @return
     */
    public List<Map<String, String>> getAllDataAndDelete() {
        // 获取所有keys (注意：大数据库环境慎用，可能导致Redis阻塞)
        Set<String> keys = stringRedisTemplate.keys("*");
        List<Map<String, String>> resultMap = new ArrayList<>();

        if (keys != null && !keys.isEmpty()) {


            for (String key : keys) {
                HashOperations<String, String, String> hashOps = stringRedisTemplate.opsForHash();
                resultMap.add(hashOps.entries(key));
            }

            // 逐个删除keys
            for (String key : keys) {
                stringRedisTemplate.delete(key);
            }
        }
        return resultMap;
    }
    
    /**
     * 获取Redis 1号数据库的StringRedisTemplate
     * @return StringRedisTemplate实例
     */
    private StringRedisTemplate getDb1StringRedisTemplate() {
        if (db1StringRedisTemplate == null) {
            // 创建Redis 1号数据库的独立连接配置
            RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
            config.setHostName("***********"); // 使用配置文件中的Redis服务器地址
            config.setPort(6378); // 使用配置文件中的Redis端口
            config.setPassword("20250519"); // 使用配置文件中的Redis密码
            config.setDatabase(1); // 设置为1号数据库
            
            // 创建独立的连接工厂
            LettuceConnectionFactory db1ConnectionFactory = new LettuceConnectionFactory(config);
            db1ConnectionFactory.afterPropertiesSet();
            
            // 创建StringRedisTemplate并设置连接工厂
            db1StringRedisTemplate = new StringRedisTemplate();
            db1StringRedisTemplate.setConnectionFactory(db1ConnectionFactory);
            db1StringRedisTemplate.afterPropertiesSet();
        }
        return db1StringRedisTemplate;
    }
    
    /**
     * 在Redis 1号数据库中添加字符串
     * @param key 键
     * @param value 值
     */
    public void setToDb1(String key, String value) {
        getDb1StringRedisTemplate().opsForValue().set(key, value);
    }
    
    /**
     * 在Redis 1号数据库中添加带过期时间的字符串
     * @param key 键
     * @param value 值
     * @param timeout 过期时间
     * @param timeUnit 时间单位
     */
    public void setToDb1WithExpiration(String key, String value, long timeout, TimeUnit timeUnit) {
        getDb1StringRedisTemplate().opsForValue().set(key, value, timeout, timeUnit);
    }
    
    /**
     * 从Redis 1号数据库中查找字符串
     * @param key 键
     * @return 值
     */
    public String getFromDb1(String key) {
        return getDb1StringRedisTemplate().opsForValue().get(key);
    }
    
    /**
     * 删除Redis 1号数据库中的键
     * @param key 键
     * @return 是否删除成功
     */
    public Boolean deleteFromDb1(String key) {
        return getDb1StringRedisTemplate().delete(key);
    }
    
    /**
     * 判断Redis 1号数据库中是否存在某个key
     * @param key 键
     * @return 是否存在该键
     */
    public Boolean hasKeyInDb1(String key) {
        return getDb1StringRedisTemplate().hasKey(key);
    }
    
    /**
     * 获取Redis 2号数据库的StringRedisTemplate
     * @return StringRedisTemplate实例
     */
    private StringRedisTemplate getDb2StringRedisTemplate() {
        if (db2StringRedisTemplate == null) {
            // 创建Redis 2号数据库的独立连接配置
            RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
            config.setHostName("***********"); // 使用配置文件中的Redis服务器地址
            config.setPort(6378); // 使用配置文件中的Redis端口
            config.setPassword("20250519"); // 使用配置文件中的Redis密码
            config.setDatabase(2); // 设置为2号数据库
            
            // 创建独立的连接工厂
            LettuceConnectionFactory db2ConnectionFactory = new LettuceConnectionFactory(config);
            db2ConnectionFactory.afterPropertiesSet();
            
            // 创建StringRedisTemplate并设置连接工厂
            db2StringRedisTemplate = new StringRedisTemplate();
            db2StringRedisTemplate.setConnectionFactory(db2ConnectionFactory);
            db2StringRedisTemplate.afterPropertiesSet();
        }
        return db2StringRedisTemplate;
    }
    
    /**
     * 在Redis 2号数据库中添加字符串
     * @param key 键
     * @param value 值
     */
    public void setToDb2(String key, String value) {
        getDb2StringRedisTemplate().opsForValue().set(key, value);
    }
    
    /**
     * 在Redis 2号数据库中添加带过期时间的字符串
     * @param key 键
     * @param value 值
     * @param timeout 过期时间
     * @param timeUnit 时间单位
     */
    public void setToDb2WithExpiration(String key, String value, long timeout, TimeUnit timeUnit) {
        getDb2StringRedisTemplate().opsForValue().set(key, value, timeout, timeUnit);
    }
    
    /**
     * 从Redis 2号数据库中查找字符串
     * @param key 键
     * @return 值
     */
    public String getFromDb2(String key) {
        return getDb2StringRedisTemplate().opsForValue().get(key);
    }
    
    /**
     * 删除Redis 2号数据库中的键
     * @param key 键
     * @return 是否删除成功
     */
    public Boolean deleteFromDb2(String key) {
        return getDb2StringRedisTemplate().delete(key);
    }
    
    /**
     * 判断Redis 2号数据库中是否存在某个key
     * @param key 键
     * @return 是否存在该键
     */
    public Boolean hasKeyInDb2(String key) {
        return getDb2StringRedisTemplate().hasKey(key);
    }

}