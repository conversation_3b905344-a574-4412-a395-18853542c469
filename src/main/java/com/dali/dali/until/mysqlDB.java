package com.dali.dali.until;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class mysqlDB {

    /**
     * 获取MySQL数据库连接
     * @param host MySQL服务器地址
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param database 数据库名
     * @return 数据库连接对象
     * @throws SQLException 数据库连接异常
     */
    public static Connection getConnection(String host, int port, String username, String password, String database) throws SQLException {
        try {
            // 加载MySQL驱动
            Class.forName("com.mysql.cj.jdbc.Driver");

            // 构建连接URL
            String url = String.format("****************************************************************************",
                                     host, port, database);

            // 获取连接
            Connection connection = DriverManager.getConnection(url, username, password);

            return connection;
        } catch (ClassNotFoundException e) {
            throw new SQLException("MySQL驱动未找到", e);
        }
    }

    /**
     * 获取MySQL数据库连接（使用默认端口3306）
     * @param host MySQL服务器地址
     * @param username 用户名
     * @param password 密码
     * @param database 数据库名
     * @return 数据库连接对象
     * @throws SQLException 数据库连接异常
     */
    public static Connection getConnection(String host, String username, String password, String database) throws SQLException {
        return getConnection(host, 3306, username, password, database);
    }

    // ==================== 查询操作 ====================

    /**
     * 执行查询操作，返回结果集
     * @param connection 数据库连接
     * @param sql SQL查询语句
     * @param params 查询参数
     * @return 查询结果列表，每行数据用Map表示
     * @throws SQLException 数据库操作异常
     *
     * 示例：
     * executeQuery(conn, "SELECT * FROM users", null)
     * executeQuery(conn, "SELECT * FROM users WHERE age > ?", 18)
     * executeQuery(conn, "SELECT * FROM users WHERE name = ? AND age > ?", "张三", 20)
     * executeQuery(conn, "SELECT id, name FROM users WHERE city = ? ORDER BY id DESC", "北京")
     */
    public static List<Map<String, Object>> executeQuery(Connection connection, String sql, Object... params) throws SQLException {
        List<Map<String, Object>> resultList = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            // 设置参数
            setParameters(pstmt, params);

            try (ResultSet rs = pstmt.executeQuery()) {
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();

                while (rs.next()) {
                    Map<String, Object> row = new HashMap<>();
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value = rs.getObject(i);
                        row.put(columnName, value);
                    }
                    resultList.add(row);
                }
            }
        }

        return resultList;
    }

    /**
     * 查询单条记录
     * @param connection 数据库连接
     * @param sql SQL查询语句
     * @param params 查询参数
     * @return 单条记录的Map，如果没有结果返回null
     * @throws SQLException 数据库操作异常
     *
     * 示例：
     * queryOne(conn, "SELECT * FROM users WHERE id = ?", 1)
     * queryOne(conn, "SELECT name, email FROM users WHERE username = ?", "admin")
     * queryOne(conn, "SELECT COUNT(*) as total FROM orders WHERE user_id = ?", 123)
     * queryOne(conn, "SELECT * FROM products WHERE name = ? AND status = ?", "iPhone", 1)
     */
    public static Map<String, Object> queryOne(Connection connection, String sql, Object... params) throws SQLException {
        List<Map<String, Object>> results = executeQuery(connection, sql, params);
        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * 查询记录数量
     * @param connection 数据库连接
     * @param sql SQL查询语句
     * @param params 查询参数
     * @return 记录数量
     * @throws SQLException 数据库操作异常
     *
     * 示例：
     * queryCount(conn, "SELECT COUNT(*) FROM users")
     * queryCount(conn, "SELECT COUNT(*) FROM users WHERE age > ?", 18)
     * queryCount(conn, "SELECT COUNT(*) FROM orders WHERE status = ? AND create_time > ?", "completed", "2023-01-01")
     * queryCount(conn, "SELECT COUNT(DISTINCT user_id) FROM orders WHERE amount > ?", 1000.0)
     */
    public static int queryCount(Connection connection, String sql, Object... params) throws SQLException {
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            setParameters(pstmt, params);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }

    // ==================== 增加操作 ====================

    /**
     * 执行插入操作
     * @param connection 数据库连接
     * @param sql SQL插入语句
     * @param params 插入参数
     * @return 影响的行数
     * @throws SQLException 数据库操作异常
     *
     * 示例：
     * executeInsert(conn, "INSERT INTO users (name, age) VALUES (?, ?)", "张三", 25)
     * executeInsert(conn, "INSERT INTO products (name, price, category_id) VALUES (?, ?, ?)", "iPhone 15", 7999.0, 1)
     * executeInsert(conn, "INSERT INTO orders (user_id, total_amount, status) VALUES (?, ?, ?)", 123, 299.99, "pending")
     * executeInsert(conn, "INSERT INTO logs (message, level, create_time) VALUES (?, ?, NOW())", "系统启动", "INFO")
     */
    public static int executeInsert(Connection connection, String sql, Object... params) throws SQLException {
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            setParameters(pstmt, params);
            return pstmt.executeUpdate();
        }
    }

    /**
     * 执行插入操作并返回自增主键
     * @param connection 数据库连接
     * @param sql SQL插入语句
     * @param params 插入参数
     * @return 自增主键值
     * @throws SQLException 数据库操作异常
     *
     * 示例：
     * executeInsertAndGetKey(conn, "INSERT INTO users (name, email) VALUES (?, ?)", "李四", "<EMAIL>")
     * executeInsertAndGetKey(conn, "INSERT INTO articles (title, content, author_id) VALUES (?, ?, ?)", "新文章", "内容...", 1)
     * executeInsertAndGetKey(conn, "INSERT INTO categories (name, parent_id) VALUES (?, ?)", "电子产品", 0)
     * executeInsertAndGetKey(conn, "INSERT INTO comments (article_id, user_id, content) VALUES (?, ?, ?)", 10, 5, "很好的文章")
     */
    public static long executeInsertAndGetKey(Connection connection, String sql, Object... params) throws SQLException {
        try (PreparedStatement pstmt = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            setParameters(pstmt, params);
            int affectedRows = pstmt.executeUpdate();

            if (affectedRows > 0) {
                try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        return generatedKeys.getLong(1);
                    }
                }
            }
        }
        return -1;
    }

    // ==================== 修改操作 ====================

    /**
     * 执行更新操作
     * @param connection 数据库连接
     * @param sql SQL更新语句
     * @param params 更新参数
     * @return 影响的行数
     * @throws SQLException 数据库操作异常
     *
     * 示例：
     * executeUpdate(conn, "UPDATE users SET age = ? WHERE id = ?", 26, 1)
     * executeUpdate(conn, "UPDATE products SET price = ?, stock = ? WHERE id = ?", 8999.0, 100, 5)
     * executeUpdate(conn, "UPDATE orders SET status = ?, update_time = NOW() WHERE order_no = ?", "shipped", "ORD20231201001")
     * executeUpdate(conn, "UPDATE users SET last_login = NOW() WHERE email = ?", "<EMAIL>")
     */
    public static int executeUpdate(Connection connection, String sql, Object... params) throws SQLException {
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            setParameters(pstmt, params);
            return pstmt.executeUpdate();
        }
    }

    // ==================== 删除操作 ====================

    /**
     * 执行删除操作
     * @param connection 数据库连接
     * @param sql SQL删除语句
     * @param params 删除参数
     * @return 影响的行数
     * @throws SQLException 数据库操作异常
     *
     * 示例：
     * executeDelete(conn, "DELETE FROM users WHERE id = ?", 1)
     * executeDelete(conn, "DELETE FROM orders WHERE status = ? AND create_time < ?", "cancelled", "2023-01-01")
     * executeDelete(conn, "DELETE FROM logs WHERE level = ? AND create_time < DATE_SUB(NOW(), INTERVAL 30 DAY)", "DEBUG")
     * executeDelete(conn, "DELETE FROM temp_data WHERE user_id = ? AND session_id = ?", 123, "sess_abc123")
     */
    public static int executeDelete(Connection connection, String sql, Object... params) throws SQLException {
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            setParameters(pstmt, params);
            return pstmt.executeUpdate();
        }
    }

    // ==================== 批量操作 ====================

    /**
     * 批量执行SQL语句
     * @param connection 数据库连接
     * @param sql SQL语句
     * @param paramsList 参数列表，每个元素是一组参数
     * @return 每条语句影响的行数数组
     * @throws SQLException 数据库操作异常
     *
     * 示例：
     * List<Object[]> params = Arrays.asList(
     *     new Object[]{"张三", 25, "<EMAIL>"},
     *     new Object[]{"李四", 30, "<EMAIL>"},
     *     new Object[]{"王五", 28, "<EMAIL>"}
     * );
     * executeBatch(conn, "INSERT INTO users (name, age, email) VALUES (?, ?, ?)", params);
     *
     * List<Object[]> updateParams = Arrays.asList(
     *     new Object[]{100, 1}, new Object[]{200, 2}, new Object[]{150, 3}
     * );
     * executeBatch(conn, "UPDATE products SET stock = ? WHERE id = ?", updateParams);
     */
    public static int[] executeBatch(Connection connection, String sql, List<Object[]> paramsList) throws SQLException {
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            for (Object[] params : paramsList) {
                setParameters(pstmt, params);
                pstmt.addBatch();
            }
            return pstmt.executeBatch();
        }
    }

    // ==================== 事务操作 ====================

    /**
     * 开始事务
     * @param connection 数据库连接
     * @throws SQLException 数据库操作异常
     *
     * 示例：
     * beginTransaction(conn);
     * // 执行多个相关的数据库操作
     * executeInsert(conn, "INSERT INTO orders ...", params1);
     * executeUpdate(conn, "UPDATE inventory ...", params2);
     * commitTransaction(conn); // 或者在异常时调用 rollbackTransaction(conn);
     */
    public static void beginTransaction(Connection connection) throws SQLException {
        connection.setAutoCommit(false);
    }

    /**
     * 提交事务
     * @param connection 数据库连接
     * @throws SQLException 数据库操作异常
     *
     * 示例：
     * beginTransaction(conn);
     * try {
     *     executeInsert(conn, "INSERT INTO users ...", params);
     *     executeUpdate(conn, "UPDATE accounts ...", params);
     *     commitTransaction(conn); // 提交所有操作
     * } catch (Exception e) {
     *     rollbackTransaction(conn); // 发生异常时回滚
     * }
     */
    public static void commitTransaction(Connection connection) throws SQLException {
        connection.commit();
        connection.setAutoCommit(true);
    }

    /**
     * 回滚事务
     * @param connection 数据库连接
     * @throws SQLException 数据库操作异常
     *
     * 示例：
     * beginTransaction(conn);
     * try {
     *     executeInsert(conn, "INSERT INTO orders ...", params);
     *     executeUpdate(conn, "UPDATE inventory ...", params);
     *     commitTransaction(conn);
     * } catch (Exception e) {
     *     rollbackTransaction(conn); // 回滚所有未提交的操作
     *     throw e;
     * }
     */
    public static void rollbackTransaction(Connection connection) throws SQLException {
        connection.rollback();
        connection.setAutoCommit(true);
    }

    // ==================== 工具方法 ====================

    /**
     * 设置PreparedStatement的参数
     * @param pstmt PreparedStatement对象
     * @param params 参数数组
     * @throws SQLException 数据库操作异常
     */
    private static void setParameters(PreparedStatement pstmt, Object... params) throws SQLException {
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }
        }
    }

    /**
     * 安全关闭数据库连接
     * @param connection 数据库连接
     *
     * 示例：
     * Connection conn = mysqlDB.getConnection("localhost", "root", "password", "testdb");
     * try {
     *     // 执行数据库操作
     *     executeQuery(conn, "SELECT * FROM users");
     * } finally {
     *     closeConnection(conn); // 确保连接被关闭
     * }
     */
    public static void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 检查连接是否有效
     * @param connection 数据库连接
     * @return 连接是否有效
     *
     * 示例：
     * Connection conn = mysqlDB.getConnection("localhost", "root", "password", "testdb");
     * if (isConnectionValid(conn)) {
     *     // 连接有效，可以执行数据库操作
     *     executeQuery(conn, "SELECT 1");
     * } else {
     *     // 连接无效，需要重新获取连接
     *     conn = mysqlDB.getConnection("localhost", "root", "password", "testdb");
     * }
     */
    public static boolean isConnectionValid(Connection connection) {
        try {
            return connection != null && !connection.isClosed() && connection.isValid(5);
        } catch (SQLException e) {
            return false;
        }
    }

    /**
     * 调试方法：检查SQL语句和参数是否匹配
     * @param sql SQL语句
     * @param params 参数数组
     * @return 调试信息
     *
     * 示例：
     * debugSQLParams("INSERT INTO users (name, age) VALUES (?, ?)", new Object[]{"张三", 25, "多余参数"})
     * -> 会返回详细的调试信息，指出参数数量不匹配
     */
    public static String debugSQLParams(String sql, Object... params) {
        StringBuilder debug = new StringBuilder();
        debug.append("=== SQL调试信息 ===\n");
        debug.append("SQL语句: ").append(sql).append("\n");

        // 计算SQL中的占位符数量
        int placeholderCount = 0;
        for (int i = 0; i < sql.length(); i++) {
            if (sql.charAt(i) == '?') {
                placeholderCount++;
            }
        }

        debug.append("SQL中占位符(?)数量: ").append(placeholderCount).append("\n");
        debug.append("提供的参数数量: ").append(params != null ? params.length : 0).append("\n");

        if (params != null) {
            debug.append("参数列表:\n");
            for (int i = 0; i < params.length; i++) {
                debug.append("  参数[").append(i + 1).append("]: ")
                     .append(params[i] != null ? params[i].toString() : "null")
                     .append(" (类型: ")
                     .append(params[i] != null ? params[i].getClass().getSimpleName() : "null")
                     .append(")\n");
            }
        }

        // 检查是否匹配
        int paramCount = params != null ? params.length : 0;
        if (placeholderCount != paramCount) {
            debug.append("\n❌ 错误: 占位符数量(").append(placeholderCount)
                 .append(")与参数数量(").append(paramCount).append(")不匹配!\n");

            if (placeholderCount > paramCount) {
                debug.append("   缺少 ").append(placeholderCount - paramCount).append(" 个参数\n");
            } else {
                debug.append("   多了 ").append(paramCount - placeholderCount).append(" 个参数\n");
            }
        } else {
            debug.append("\n✅ 占位符数量与参数数量匹配\n");
        }

        debug.append("==================\n");
        return debug.toString();
    }

    /**
     * 安全执行插入操作，带调试信息
     * @param connection 数据库连接
     * @param sql SQL插入语句
     * @param params 插入参数
     * @return 影响的行数
     * @throws SQLException 数据库操作异常
     *
     * 示例：
     * executeInsertWithDebug(conn, "INSERT INTO users (name, age) VALUES (?, ?)", "张三", 25)
     * 如果参数不匹配，会在异常信息中包含详细的调试信息
     */
    public static int executeInsertWithDebug(Connection connection, String sql, Object... params) throws SQLException {
        try {
            return executeInsert(connection, sql, params);
        } catch (SQLException e) {
            // 如果是参数不匹配的错误，添加调试信息
            if (e.getMessage().contains("Column count doesn't match value count") ||
                e.getMessage().contains("Parameter index out of range")) {
                String debugInfo = debugSQLParams(sql, params);
                System.err.println(debugInfo);
                throw new SQLException("SQL参数不匹配错误:\n" + debugInfo + "\n原始错误: " + e.getMessage(), e);
            }
            throw e;
        }
    }
}
