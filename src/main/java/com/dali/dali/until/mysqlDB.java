package com.dali.dali.until;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class mysqlDB {

    /**
     * 获取MySQL数据库连接
     * @param host MySQL服务器地址
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param database 数据库名
     * @return 数据库连接对象
     * @throws SQLException 数据库连接异常
     */
    public static Connection getConnection(String host, int port, String username, String password, String database) throws SQLException {
        try {
            // 加载MySQL驱动
            Class.forName("com.mysql.cj.jdbc.Driver");

            // 构建连接URL
            String url = String.format("****************************************************************************",
                                     host, port, database);

            // 获取连接
            Connection connection = DriverManager.getConnection(url, username, password);

            return connection;
        } catch (ClassNotFoundException e) {
            throw new SQLException("MySQL驱动未找到", e);
        }
    }

    /**
     * 获取MySQL数据库连接（使用默认端口3306）
     * @param host MySQL服务器地址
     * @param username 用户名
     * @param password 密码
     * @param database 数据库名
     * @return 数据库连接对象
     * @throws SQLException 数据库连接异常
     */
    public static Connection getConnection(String host, String username, String password, String database) throws SQLException {
        return getConnection(host, 3306, username, password, database);
    }

    // ==================== 查询操作 ====================

    /**
     * 执行查询操作，返回结果集
     * @param connection 数据库连接
     * @param sql SQL查询语句
     * @param params 查询参数
     * @return 查询结果列表，每行数据用Map表示
     * @throws SQLException 数据库操作异常
     */
    public static List<Map<String, Object>> executeQuery(Connection connection, String sql, Object... params) throws SQLException {
        List<Map<String, Object>> resultList = new ArrayList<>();

        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            // 设置参数
            setParameters(pstmt, params);

            try (ResultSet rs = pstmt.executeQuery()) {
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();

                while (rs.next()) {
                    Map<String, Object> row = new HashMap<>();
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value = rs.getObject(i);
                        row.put(columnName, value);
                    }
                    resultList.add(row);
                }
            }
        }

        return resultList;
    }

    /**
     * 查询单条记录
     * @param connection 数据库连接
     * @param sql SQL查询语句
     * @param params 查询参数
     * @return 单条记录的Map，如果没有结果返回null
     * @throws SQLException 数据库操作异常
     */
    public static Map<String, Object> queryOne(Connection connection, String sql, Object... params) throws SQLException {
        List<Map<String, Object>> results = executeQuery(connection, sql, params);
        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * 查询记录数量
     * @param connection 数据库连接
     * @param sql SQL查询语句
     * @param params 查询参数
     * @return 记录数量
     * @throws SQLException 数据库操作异常
     */
    public static int queryCount(Connection connection, String sql, Object... params) throws SQLException {
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            setParameters(pstmt, params);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }

    // ==================== 增加操作 ====================

    /**
     * 执行插入操作
     * @param connection 数据库连接
     * @param sql SQL插入语句
     * @param params 插入参数
     * @return 影响的行数
     * @throws SQLException 数据库操作异常
     */
    public static int executeInsert(Connection connection, String sql, Object... params) throws SQLException {
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            setParameters(pstmt, params);
            return pstmt.executeUpdate();
        }
    }

    /**
     * 执行插入操作并返回自增主键
     * @param connection 数据库连接
     * @param sql SQL插入语句
     * @param params 插入参数
     * @return 自增主键值
     * @throws SQLException 数据库操作异常
     */
    public static long executeInsertAndGetKey(Connection connection, String sql, Object... params) throws SQLException {
        try (PreparedStatement pstmt = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            setParameters(pstmt, params);
            int affectedRows = pstmt.executeUpdate();

            if (affectedRows > 0) {
                try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        return generatedKeys.getLong(1);
                    }
                }
            }
        }
        return -1;
    }

    // ==================== 修改操作 ====================

    /**
     * 执行更新操作
     * @param connection 数据库连接
     * @param sql SQL更新语句
     * @param params 更新参数
     * @return 影响的行数
     * @throws SQLException 数据库操作异常
     */
    public static int executeUpdate(Connection connection, String sql, Object... params) throws SQLException {
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            setParameters(pstmt, params);
            return pstmt.executeUpdate();
        }
    }

    // ==================== 删除操作 ====================

    /**
     * 执行删除操作
     * @param connection 数据库连接
     * @param sql SQL删除语句
     * @param params 删除参数
     * @return 影响的行数
     * @throws SQLException 数据库操作异常
     */
    public static int executeDelete(Connection connection, String sql, Object... params) throws SQLException {
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            setParameters(pstmt, params);
            return pstmt.executeUpdate();
        }
    }

    // ==================== 批量操作 ====================

    /**
     * 批量执行SQL语句
     * @param connection 数据库连接
     * @param sql SQL语句
     * @param paramsList 参数列表，每个元素是一组参数
     * @return 每条语句影响的行数数组
     * @throws SQLException 数据库操作异常
     */
    public static int[] executeBatch(Connection connection, String sql, List<Object[]> paramsList) throws SQLException {
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            for (Object[] params : paramsList) {
                setParameters(pstmt, params);
                pstmt.addBatch();
            }
            return pstmt.executeBatch();
        }
    }

    // ==================== 事务操作 ====================

    /**
     * 开始事务
     * @param connection 数据库连接
     * @throws SQLException 数据库操作异常
     */
    public static void beginTransaction(Connection connection) throws SQLException {
        connection.setAutoCommit(false);
    }

    /**
     * 提交事务
     * @param connection 数据库连接
     * @throws SQLException 数据库操作异常
     */
    public static void commitTransaction(Connection connection) throws SQLException {
        connection.commit();
        connection.setAutoCommit(true);
    }

    /**
     * 回滚事务
     * @param connection 数据库连接
     * @throws SQLException 数据库操作异常
     */
    public static void rollbackTransaction(Connection connection) throws SQLException {
        connection.rollback();
        connection.setAutoCommit(true);
    }

    // ==================== 工具方法 ====================

    /**
     * 设置PreparedStatement的参数
     * @param pstmt PreparedStatement对象
     * @param params 参数数组
     * @throws SQLException 数据库操作异常
     */
    private static void setParameters(PreparedStatement pstmt, Object... params) throws SQLException {
        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }
        }
    }

    /**
     * 安全关闭数据库连接
     * @param connection 数据库连接
     */
    public static void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 检查连接是否有效
     * @param connection 数据库连接
     * @return 连接是否有效
     */
    public static boolean isConnectionValid(Connection connection) {
        try {
            return connection != null && !connection.isClosed() && connection.isValid(5);
        } catch (SQLException e) {
            return false;
        }
    }
}
