package com.dali.dali.until;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class mysqlDB {

    /**
     * 获取MySQL数据库连接
     * @param host MySQL服务器地址
     * @param port 端口号
     * @param username 用户名
     * @param password 密码
     * @param database 数据库名
     * @return 数据库连接对象
     * @throws SQLException 数据库连接异常
     */
    public static Connection getConnection(String host, int port, String username, String password, String database) throws SQLException {
        try {
            // 加载MySQL驱动
            Class.forName("com.mysql.cj.jdbc.Driver");

            // 构建连接URL
            String url = String.format("****************************************************************************",
                                     host, port, database);

            // 获取连接
            Connection connection = DriverManager.getConnection(url, username, password);

            return connection;
        } catch (ClassNotFoundException e) {
            throw new SQLException("MySQL驱动未找到", e);
        }
    }

    /**
     * 获取MySQL数据库连接（使用默认端口3306）
     * @param host MySQL服务器地址
     * @param username 用户名
     * @param password 密码
     * @param database 数据库名
     * @return 数据库连接对象
     * @throws SQLException 数据库连接异常
     */
    public static Connection getConnection(String host, String username, String password, String database) throws SQLException {
        return getConnection(host, 3306, username, password, database);
    }
}
