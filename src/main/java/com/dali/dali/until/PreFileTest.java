package com.dali.dali.until;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;


public class PreFileTest {

    public static void main(String[] args) {



        HttpResponse response = HttpRequest.get("http://10.11.2.160:3006/api/core/dataset/collection/read")
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer fastgpt-qMFiTDwGRKIwU2ZPD9svw115pnqaSPTu2l6LMbnooMeg2pYO79Whg3w")
                .body("{\"collectionId\": \"68417b886130313d1d01fc89\"}")
                .execute();

        JSONObject jsonObject = JSON.parseObject(response.body());
        String code = jsonObject.getString("code");

        JSONObject dataObject = jsonObject.getJSONObject("data");
        String urlss = dataObject.getString("value");


        String url = "http://192.168.1.225:8080/datafile/研发-TPD项目修正.xlsx";
        url = "http://10.11.2.160:3006"+urlss;
        // Base64 编码
        String base64Url = Base64.getEncoder().encodeToString(url.getBytes());

        String username = "苏二川";
        String jobnumber = "012652";

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String newTime = dateFormat.format(new Date());

        String base64UrlF = base64Url+
                "&watermarkTxt="+username+"-"+jobnumber+"_"+newTime;

        // 构造最终的预览 URL
        String previewUrl = "http://127.0.0.1:8013/onlinePreview?url=" + base64UrlF;

        // 输出或使用该 URL
        System.out.println("Preview URL: " + previewUrl);

    }
}
