package com.dali.dali.until;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

//工具类
public class HttpsPostRequestExample {

    /***
     * 根据url获取详情片段
     * @param url
     * @return
     */
    public static Map sendHutool2(String url){
        Map reMap = new HashMap<>();

        String authorization = "fastgpt-qMFiTDwGRKIwU2ZPD9svw115pnqaSPTu2l6LMbnooMeg2pYO79Whg3w";  //正式
//        String authorization = "fastgpt-ePo9lbSV5IW2hFRGWrM0bV10ZqRCERHf4K63QgsoHnp00Eim1LzgIq"; //测试
        HttpResponse response = HttpRequest.get(url)
                .header("Authorization", "Bearer " + authorization)
                .execute();

        JSONObject jsonObject = JSONObject.parseObject(response.body());

        JSONObject dataObject = jsonObject.getJSONObject("data");

        String content = dataObject.getString("q");

//        System.out.println(content);
        reMap.put("content",content.replaceAll("\n", "<br>"));
        // 其他属性的解析可以类似地进行
        String sourceName = dataObject.getString("sourceName");
        //添加集合id，用于获取集合信息 2025-06-13
        String collectionId = dataObject.getString("collectionId");
        reMap.put("collectionId",collectionId);
        reMap.put("sourceName",sourceName);
        return reMap;
    }


    /***
     * 根据collectionId获取文件的url地址,是可以直接打开的地址
     * @param collectionId
     * @return
     */
    public static String getFileUrlByCollectionId(String collectionId,
                                                  String username,String jobnumber,String newTime){

        HttpResponse response = HttpRequest.get("http://10.11.2.160:3006/api/core/dataset/collection/read")
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer fastgpt-qMFiTDwGRKIwU2ZPD9svw115pnqaSPTu2l6LMbnooMeg2pYO79Whg3w")
                .body("{\"collectionId\":\"" +collectionId+ "\"}")
                .execute();


        JSONObject jsonObject = JSON.parseObject(response.body());
        String code = jsonObject.getString("code");

        JSONObject dataObject = jsonObject.getJSONObject("data");
        String urlss = dataObject.getString("value");


        String url = "http://10.11.2.160:3006"+urlss;
        // Base64 编码
        String base64Url = Base64.getEncoder().encodeToString(url.getBytes());

        String base64UrlF = base64Url+
                "&watermarkTxt="+username+"-"+jobnumber+"_"+newTime;

        String previewUrl = "http://ai.bodor.com:3001/onlinePreview?url=" + base64UrlF;

        return previewUrl;
    }
}
