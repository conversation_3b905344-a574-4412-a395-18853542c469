# 应用服务 WEB 访问端口
server:
  # 端口名
  port: 4003
  servlet:
    encoding:   # 配置中文页面乱码
      force: true
      enabled: true
      charset: UTF-8
  ssl:
    key-store: classpath:bodor.com.jks
    key-store-password: PxRDbuYp
    key-store-type: JKS

spring:
  thymeleaf:
    mode: HTML5
    encoding: UTF-8
    servlet:
      content-type: text/html
    cache: false
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
  redis:
    # 地址
    #    host: localhost
    host: ***********
    # 端口，默认为6379
    port: 6378
    # 数据库索引
    database: 0
    # 密码
    password: 20250519
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms