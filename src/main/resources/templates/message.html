<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <title>知识位置</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            padding: 20px;
            position: relative;
        }

        /* 水印容器样式 */
        .watermark-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            pointer-events: none;
            overflow: hidden;
        }

        h1 {
            font-size: 24px;
            color: #007BFF;
            margin-bottom: 20px;
        }
        ul {
            list-style: none;
            padding: 0;
        }
        li {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
        h4 {
            font-size: 18px;
            color: palevioletred;
            margin-bottom: 10px;
        }
        .filecount {
            font-size: 18px;
            line-height: 1.6;
        }

        .filename {
            font-size: 18px;
            line-height: 1.6;
            text-indent: 2em; /* 首行缩进两格 */
        }
    </style>
</head>
<body>
<!-- 水印容器 -->
<div class="watermark-container">
    <div class="watermark" id="watermark"></div>
</div>

<h1>知识来源：</h1>
<ul>
    <li th:each="mapItem : ${list}">
        <h4>内容片段:</h4>
        <p th:utext="${mapItem.content}" class="filecount"></p>

        <h4>引用文件:</h4>
        <a th:href="${mapItem.fileurl}" th:text="${mapItem.sourceName}" class="filename" target="_blank"></a>
    </li>
</ul>

<script>
    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        try {
            // 从后端获取完整水印配置
            const watermarkConfig = {
                width: parseInt("[[${watermarkConfig.width}]]"),
                height: parseInt("[[${watermarkConfig.height}]]"),
                text: "[[${watermarkConfig.text}]]",
                opacity: parseFloat("[[${watermarkConfig.opacity}]]"),
                rotate: parseFloat("[[${watermarkConfig.rotate}]]"),
                fontSize: parseInt("[[${watermarkConfig.fontSize}]]"),
                color: "[[${watermarkConfig.color}]]",
                fontWeight: "[[${watermarkConfig.fontWeight}]]",
                gapX: parseInt("[[${watermarkConfig.gapX}]]"),
                gapY: parseInt("[[${watermarkConfig.gapY}]]"),
                randomOffset: parseInt("[[${watermarkConfig.randomOffset}]]") || 30, // 随机偏移量，默认30px
                useGrid: [[${watermarkConfig.useGrid}]] || false // 是否使用网格系统，默认false
            };

            // console.log('完整水印配置:', watermarkConfig);

            generateWatermarks(watermarkConfig);

            // 窗口大小改变时重新生成水印
            window.addEventListener('resize', function() {
                generateWatermarks(watermarkConfig);
            });
        } catch (error) {
            console.error('初始化水印时出错:', error);
        }
    });

    function generateWatermarks(config) {
        try {
            const watermarkContainer = document.getElementById('watermark');
            watermarkContainer.innerHTML = '';

            // 获取容器尺寸
            const containerWidth = window.innerWidth;
            const containerHeight = window.innerHeight;

            // 计算文本实际宽度和高度（考虑旋转因素）
            const textWidth = config.width;
            const textHeight = config.height;

            if (config.useGrid) {
                // 方法1：使用网格系统（原有方式，但优化计算）
                generateWatermarksWithGrid(config, containerWidth, containerHeight, textWidth, textHeight);
            } else {
                // 方法2：使用绝对定位（更均匀的分布）
                generateWatermarksWithPosition(config, containerWidth, containerHeight, textWidth, textHeight);
            }

            // console.log('水印生成完成');
        } catch (error) {
            console.error('生成水印过程中出错:', error);
        }
    }

    // 使用网格系统生成水印
    function generateWatermarksWithGrid(config, containerWidth, containerHeight, textWidth, textHeight) {
        const watermarkContainer = document.getElementById('watermark');

        // 设置水印网格样式
        watermarkContainer.style.display = 'grid';
        watermarkContainer.style.gridTemplateColumns = `repeat(auto-fill, ${textWidth + config.gapX}px)`;
        watermarkContainer.style.gridTemplateRows = `repeat(auto-fill, ${textHeight + config.gapY}px)`;

        // 计算需要的水印数量
        const cols = Math.ceil(containerWidth / (textWidth + config.gapX));
        const rows = Math.ceil(containerHeight / (textHeight + config.gapY));
        const totalWatermarks = cols * rows;

        // console.log('水印数量计算（网格系统）:', {cols, rows, totalWatermarks});

        // 生成水印元素
        for (let i = 0; i < totalWatermarks; i++) {
            const watermarkItem = createWatermarkItem(config);
            watermarkContainer.appendChild(watermarkItem);
        }
    }

    // 使用绝对定位生成水印
    function generateWatermarksWithPosition(config, containerWidth, containerHeight, textWidth, textHeight) {
        const watermarkContainer = document.getElementById('watermark');
        watermarkContainer.style.position = 'relative';

        // 计算水平和垂直方向的水印数量，增加10%的冗余以确保覆盖
        const cols = Math.ceil(containerWidth / (textWidth * 0.7));
        const rows = Math.ceil(containerHeight / (textHeight * 0.7));

        const totalWatermarks = cols * rows;
        // console.log('水印数量计算（绝对定位）:', {cols, rows, totalWatermarks});

        // 计算实际的水平和垂直间距，确保均匀分布
        const actualGapX = containerWidth / (cols - 1);
        const actualGapY = containerHeight / (rows - 1);

        // 生成水印元素
        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                const watermarkItem = createWatermarkItem(config);

                // 计算位置，添加随机偏移
                const offsetX = col * actualGapX + (Math.random() * config.randomOffset * 2 - config.randomOffset);
                const offsetY = row * actualGapY + (Math.random() * config.randomOffset * 2 - config.randomOffset);

                // 设置绝对定位
                watermarkItem.style.position = 'absolute';
                watermarkItem.style.left = `${offsetX}px`;
                watermarkItem.style.top = `${offsetY}px`;

                watermarkContainer.appendChild(watermarkItem);
            }
        }
    }

    // 创建单个水印元素
    function createWatermarkItem(config) {
        const watermarkItem = document.createElement('div');

        // 应用水印项样式
        watermarkItem.style.display = 'flex';
        watermarkItem.style.justifyContent = 'center';
        watermarkItem.style.alignItems = 'center';
        watermarkItem.style.opacity = config.opacity;
        watermarkItem.style.transform = `rotate(${config.rotate}deg)`;
        watermarkItem.style.width = `${config.width}px`;
        watermarkItem.style.height = `${config.height}px`;
        watermarkItem.style.pointerEvents = 'none';

        const watermarkTextEl = document.createElement('div');

        // 应用水印文本样式
        watermarkTextEl.style.fontSize = `${config.fontSize}px`;
        watermarkTextEl.style.color = config.color;
        watermarkTextEl.style.fontWeight = config.fontWeight;
        watermarkTextEl.style.textAlign = 'center';
        watermarkTextEl.style.whiteSpace = 'nowrap';
        watermarkTextEl.style.overflow = 'hidden';
        watermarkTextEl.style.textOverflow = 'ellipsis';
        watermarkTextEl.textContent = config.text;

        watermarkItem.appendChild(watermarkTextEl);
        return watermarkItem;
    }

</script>
</body>
</html>