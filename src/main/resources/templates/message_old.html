<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <title>知识位置</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            padding: 20px;
        }
        h1 {
            font-size: 24px;
            color: #007BFF;
            margin-bottom: 20px;
        }
        ul {
            list-style: none;
            padding: 0;
        }
        li {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
        h4 {
            font-size: 18px;
            color: palevioletred;
            margin-bottom: 10px;
        }
        .filecount {
            font-size: 18px;
            line-height: 1.6;
        }

        .filename {
            font-size: 18px;
            line-height: 1.6;
            text-indent: 2em; /* 首行缩进两格 */
        }

    </style>
</head>
<body>
<h1>知识来源：</h1>
<ul>
    <li th:each="mapItem : ${list}">
        <h4 >内容片段:</h4>
        <p th:utext="${mapItem.content}" class="filecount"></p>

        <h4>引用文件:</h4>
        <a th:href="${mapItem.fileurl}" th:text="${mapItem.sourceName}" class="filename" target="_blank"></a>
    </li>
</ul>
</body>
</html>