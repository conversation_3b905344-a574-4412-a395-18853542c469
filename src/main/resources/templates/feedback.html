<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">

    <!-- 页面图标 -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>员工反馈系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4361ee;  /* 信任蓝 */
            --secondary: #3a0ca3; /* 权威深蓝 */
            --accent: #f72585;   /* 行动粉 */
            --success: #4cc9f0;  /* 积极青 */
            --error: #f94144;    /* 警示红 */
            --neutral: #f8f9fa;  /* 背景灰 */
            --text: #212529;     /* 主文本 */
            --text-light: #6c757d;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Noto Sans SC', sans-serif;
        }

        body {
            background-color: var(--neutral);
            color: var(--text);
            line-height: 1.6;
            padding: 20px;
            background-image: radial-gradient(circle at 10% 20%, rgba(67, 97, 238, 0.05) 0%, rgba(67, 97, 238, 0.05) 90%);
        }

        .feedback-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 90vh;
        }

        .feedback-form {
            max-width: 560px;
            width: 100%;
            margin: 30px auto;
            padding: 40px;
            background: white;
            border-radius: 16px;
            box-shadow:
                    0 4px 6px rgba(0, 0, 0, 0.05),
                    0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            position: relative;
            overflow: hidden;
        }

        .feedback-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 8px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary), var(--secondary));
        }

        .feedback-form:hover {
            box-shadow:
                    0 10px 20px rgba(0, 0, 0, 0.1),
                    0 6px 6px rgba(0, 0, 0, 0.05);
            transform: translateY(-2px);
        }

        h2 {
            color: var(--secondary);
            margin-bottom: 30px;
            text-align: center;
            font-weight: 700;
            font-size: 28px;
            position: relative;
            padding-bottom: 15px;
        }

        h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 4px;
            background: linear-gradient(to right, var(--primary), var(--accent));
            border-radius: 2px;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        label {
            display: block;
            margin-bottom: 10px;
            font-weight: 500;
            color: var(--secondary);
            font-size: 16px;
        }

        textarea, input[type="file"] {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        }

        textarea {
            min-height: 160px;
            resize: vertical;
            line-height: 1.5;
        }

        textarea:focus, input[type="file"]:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 4px rgba(67, 97, 238, 0.15);
            background-color: white;
        }

        textarea::placeholder {
            color: #adb5bd;
            font-weight: 300;
        }

        .hint {
            font-size: 14px;
            color: var(--text-light);
            margin-top: 8px;
            font-weight: 300;
        }

        .file-input-container {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }

        .file-input-button {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .file-input-button:hover {
            border-color: var(--primary);
            background-color: rgba(67, 97, 238, 0.05);
        }

        .file-input-button i {
            font-size: 24px;
            margin-bottom: 10px;
            color: var(--primary);
        }

        input[type="file"] {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-preview {
            margin-top: 15px;
            padding: 12px 15px;
            background-color: #f1f8ff;
            border: 1px solid #d0e3ff;
            border-radius: 8px;
            display: none;
            align-items: center;
            animation: fadeIn 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .file-preview.show {
            display: flex;
        }

        .file-icon {
            margin-right: 12px;
            color: var(--primary);
            font-size: 22px;
            width: 24px;
            text-align: center;
        }

        .file-name {
            flex-grow: 1;
            font-size: 15px;
            color: var(--text);
            word-break: break-all;
            font-weight: 500;
        }

        .file-size {
            font-size: 13px;
            color: var(--text-light);
            margin-left: 8px;
        }

        .remove-file {
            color: var(--error);
            cursor: pointer;
            margin-left: 15px;
            font-size: 18px;
            transition: transform 0.2s ease;
        }

        .remove-file:hover {
            transform: scale(1.1);
        }

        button[type="submit"] {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border: none;
            padding: 16px;
            font-size: 18px;
            border-radius: 8px;
            cursor: pointer;
            width: 100%;
            font-weight: 500;
            letter-spacing: 0.5px;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-top: 10px;
            text-transform: uppercase;
        }

        button[type="submit"]:hover {
            transform: translateY(-2px);
            box-shadow: 0 7px 14px rgba(0, 0, 0, 0.15);
            opacity: 0.95;
        }

        button[type="submit"]:active {
            transform: translateY(0);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .error-message {
            color: var(--error);
            background-color: rgba(249, 65, 68, 0.1);
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            border-left: 4px solid var(--error);
            display: flex;
            align-items: center;
        }

        .success-message {
            color: #2b8a3e;
            background-color: rgba(46, 204, 113, 0.1);
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            border-left: 4px solid #2b8a3e;
            display: flex;
            align-items: center;
        }

        .message-icon {
            margin-right: 12px;
            font-size: 20px;
        }

        @media (max-width: 640px) {
            .feedback-form {
                padding: 30px 20px;
                margin: 15px;
                border-radius: 12px;
            }

            h2 {
                font-size: 24px;
                padding-bottom: 12px;
            }

            h2::after {
                width: 80px;
            }
        }

        /* 微交互增强 */
        .form-group:focus-within label {
            color: var(--primary);
        }

        .progress-bar {
            height: 4px;
            background: linear-gradient(to right, var(--success), var(--primary));
            width: 0;
            transition: width 0.3s ease;
            margin-top: 8px;
            border-radius: 2px;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
<div class="feedback-container">
    <div class="feedback-form">
        <h2>问题反馈</h2>

        <!-- 显示错误信息 -->
        <div th:if="${error}" class="error-message">
            <span class="message-icon"><i class="fas fa-exclamation-circle"></i></span>
            <span th:text="${error}"></span>
        </div>
        <div th:if="${success}" class="success-message">
            <span class="message-icon"><i class="fas fa-check-circle"></i></span>
            <span th:text="${success}"></span>
        </div>

        <form method="POST" th:action="@{/feedback}" enctype="multipart/form-data">
            <div class="form-group">
                <input type="text" id="jobNumber" name="jobNumber" th:value="${jobNumber}" style="display: none;">
            </div>

            <div class="form-group">
                <label for="content">您的宝贵意见</label>
                <textarea id="content" name="content" rows="5" required
                          placeholder="我们珍视每一条反馈...&#10;请详细描述您遇到的问题或建议..."></textarea>
                <div class="progress-bar" id="content-progress"></div>
            </div>

            <div class="form-group">
                <label>上传附件（可选）</label>
                <div class="file-input-container">
                    <div class="file-input-button">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <span>点击或拖拽文件到此处</span>
                        <p class="hint">支持图片、文档等格式，最大10MB</p>
                    </div>
                    <input type="file" id="attachment" name="attachment">
                    <div class="file-preview" id="file-preview">
                        <i class="file-icon fas fa-file-alt" id="file-icon"></i>
                        <div style="flex-grow: 1;">
                            <div class="file-name" id="file-name"></div>
                            <div class="file-size" id="file-size"></div>
                        </div>
                        <i class="remove-file fas fa-times" id="remove-file" title="移除文件"></i>
                    </div>
                </div>
            </div>

            <button type="submit">
                <i class="fas fa-paper-plane" style="margin-right: 8px;"></i>提交反馈
            </button>
        </form>
    </div>
</div>

<script>
    // 文本输入进度反馈
    const textarea = document.getElementById('content');
    const progressBar = document.getElementById('content-progress');

    textarea.addEventListener('input', function() {
        const length = this.value.length;
        const progress = Math.min(100, length / 2); // 每2个字符增加1%进度
        progressBar.style.width = progress + '%';

        // 颜色变化反馈
        if(progress > 80) {
            progressBar.style.background = `linear-gradient(to right, #4cc9f0, #4895ef)`;
        } else if(progress > 50) {
            progressBar.style.background = `linear-gradient(to right, #4895ef, #4361ee)`;
        } else {
            progressBar.style.background = `linear-gradient(to right, #f72585, #4895ef)`;
        }
    });

    // 文件上传显示
    const fileInput = document.getElementById('attachment');
    const filePreview = document.getElementById('file-preview');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');
    const fileIcon = document.getElementById('file-icon');
    const removeFile = document.getElementById('remove-file');

    fileInput.addEventListener('change', function(e) {
        if(this.files.length > 0) {
            const file = this.files[0];
            fileName.textContent = file.name;

            // 显示文件大小
            const sizeInMB = (file.size / (1024 * 1024)).toFixed(4);
            fileSize.textContent = `${sizeInMB} MB`;

            // 根据文件类型设置图标
            if(file.type.includes('image')) {
                fileIcon.className = 'file-icon fas fa-file-image';
            } else if(file.type.includes('pdf')) {
                fileIcon.className = 'file-icon fas fa-file-pdf';
            } else if(file.type.includes('word')) {
                fileIcon.className = 'file-icon fas fa-file-word';
            } else if(file.type.includes('excel')) {
                fileIcon.className = 'file-icon fas fa-file-excel';
            } else if(file.type.includes('zip') || file.type.includes('compressed')) {
                fileIcon.className = 'file-icon fas fa-file-archive';
            } else {
                fileIcon.className = 'file-icon fas fa-file-alt';
            }

            // 显示预览区域
            filePreview.classList.add('show');
        }
    });

    // 移除文件功能
    removeFile.addEventListener('click', function(e) {
        e.stopPropagation();
        fileInput.value = '';
        filePreview.classList.remove('show');
    });

    // 拖放功能增强
    const fileButton = document.querySelector('.file-input-button');

    fileButton.addEventListener('dragover', (e) => {
        e.preventDefault();
        fileButton.style.borderColor = 'var(--primary)';
        fileButton.style.backgroundColor = 'rgba(67, 97, 238, 0.1)';
    });

    fileButton.addEventListener('dragleave', () => {
        fileButton.style.borderColor = '#dee2e6';
        fileButton.style.backgroundColor = '#f8f9fa';
    });

    fileButton.addEventListener('drop', (e) => {
        e.preventDefault();
        fileButton.style.borderColor = '#dee2e6';
        fileButton.style.backgroundColor = '#f8f9fa';

        if(e.dataTransfer.files.length) {
            fileInput.files = e.dataTransfer.files;
            const event = new Event('change');
            fileInput.dispatchEvent(event);
        }
    });
</script>
</body>
</html>